import { CRUDServiceImpl } from "../crudService";
import { GameGroupFilterDBInstance, GameGroupFilterModel } from "../../models/gamegroupFilter";
import { GameGroupFilter } from "../../entities/gamegroup";
import { ValidationError } from "../../errors";
import { validatePositiveNumber } from "../../utils/common";
import { lazy } from "@skywind-group/sw-utils";
import * as sequelize from "sequelize";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { Models } from "../../models/models";

const GameGroupModel = Models.GameGroupModel;
const GameGroupFilterModel = Models.GameGroupFilterModel;

export class GameGroupFilterService
    extends CRUDServiceImpl<GameGroupFilterDBInstance, GameGroupFilter, GameGroupFilterModel> {

    public gameGroupModel = GameGroupModel;

    public getModel(): GameGroupFilterModel {
        return GameGroupFilterModel;
    }

    public async create(data: GameGroupFilter,
                        transaction?: sequelize.Transaction): Promise<GameGroupFilterDBInstance> {
        return super.create(data, transaction);
    }

    protected validateCreateData(data: GameGroupFilter): GameGroupFilter {
        return this.validateFilter(data);
    }

    protected validateUpdateData(data: Partial<GameGroupFilter>): Partial<GameGroupFilter> {
        return this.validateFilter(data as any);
    }

    protected validateFilter(data: GameGroupFilter): GameGroupFilter {
        const fields = ["maxTotalBet", "minTotalBet", "defTotalBet", "winCapping", "maxExposure"];
        let atLeastOneFieldExists = false;
        for (const field of fields) {
            if (field in data) {
                atLeastOneFieldExists = true;
                if (!validatePositiveNumber(data[field])) {
                    throw new ValidationError(`${field} should be a positive number`);
                }
            }
        }

        if (!atLeastOneFieldExists) {
            throw new ValidationError(`${fields} one of these fields are required`);
        }

        if ("maxTotalBet" in data && "minTotalBet" in data && data.maxTotalBet <= data.minTotalBet) {
            throw new ValidationError("Max total bet should be more than min total bet");
        }

        if ("defTotalBet" in data) {
            if ("minTotalBet" in data && data.defTotalBet < data.minTotalBet) {
                throw new ValidationError("Default total bet should be more than min total bet");
            }

            if ("maxTotalBet" in data && data.defTotalBet > data.maxTotalBet) {
                throw new ValidationError("Default total bet should be less than max total bet");
            }
        }

        if (!Array.isArray(data.games)) {
            throw new ValidationError("data.games should be an array");
        }

        if (!Array.isArray(data.currencies)) {
            throw new ValidationError("data.currencies should be an array");
        }

        for (const code of data.currencies) {
            const currency = Currencies.value(code);
            if (!currency) {
                throw new ValidationError("data.currencies should exist");
            }
        }

        return {
            winCapping: data.winCapping || null,
            minTotalBet: data.minTotalBet || null,
            maxTotalBet: data.maxTotalBet || null,
            defTotalBet: data.defTotalBet || null,
            maxExposure: data.maxExposure || null,
            currencies: data.currencies,
            games: data.games,
            groupId: data.groupId,
            ignoreInvalid: !!data.ignoreInvalid
        };
    }
}

export const gameGroupFilterService = lazy(() => new GameGroupFilterService());
