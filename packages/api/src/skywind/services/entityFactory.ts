import { createEntityByType } from "./entity";
import { Transaction, UniqueConstraintError } from "sequelize";
import { DeferredPaymentMethod } from "@skywind-group/sw-deferred-payment";
import {
    BaseEntity,
    ChildEntity,
    Entity,
    ENTITY_TYPE,
    EntityInfo, EntityStatus,
    WithBalances,
    WithMerchant
} from "../entities/entity";
import { BrandEntity } from "../entities/brand";
import { Merchant, MerchantParams } from "../entities/merchant";
import { sequelize as db } from "../storage/db";
import { getMerchantCRUDService } from "./merchant";
import * as Errors from "../errors";
import { DOMAIN_PATTERN, LANGUAGES } from "../utils/common";
import EntitySettingsService, { getEntitiesSettings, getEntitySettings } from "./settings";
import EntityCache from "../cache/entity";
import { validateEntityStatus, validateEntityTestStatus } from "../utils/validateEntityStatus";
import { getDeploymentGroupService } from "./deploymentGroup";
import { getEntityJurisdictionService } from "./entityJurisdiction";
import { Label } from "../entities/label";
import { EntitySettingsUpdate } from "../entities/settings";
import { getAvailableSiteService } from "./availableSites";
import { AVAILABLE_SITE_STATUSES } from "../entities/availableSite";
import { getJurisdictionService } from "./jurisdiction";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { Models } from "../models/models";
import { defineDefaultStatusForNewEntity } from "../utils/defineDefaultStatusForNewEntity";
import { validateCreateCountries, validateCreateDefaultCountry, validateUpdateCountries, validateUpdateDefaultCountry } from "../utils/validateEntityCountries";

const EntityModel = Models.EntityModel;

export type MerchantInfoWithBalance = EntityInfo & WithBalances & WithMerchant;

const DEFAULT_MERCHANT_ENTITY_SETTINGS: EntitySettingsUpdate = {
    splitPayment: true,
    bonusPaymentMethod: DeferredPaymentMethod.MANUAL,
    newDeferredPaymentPromoId: true
};

export interface CreateData {
    type?: string;
    name: string;
    description?: string;
    defaultCurrency: string;
    defaultCountry: string;
    defaultLanguage: string;
    countries?: Array<string>;
    currencies?: Array<string>;
    languages?: Array<string>;
    isTest?: boolean;
    title?: string;
    key?: string;
    domains?: string[];
    labels?: Label[];
    merchantTypes?: string[];
    merchantTypesInherited?: boolean;
    merchantType?: string;
    deploymentGroupRoute?: string;
    deploymentGroupId?: number;
    jurisdictionCode: string;
    webSiteUrl?: string;
}

export interface UpdateData {
    status?: string;
    description?: string;
    defaultCurrency?: string;
    defaultCountry?: string;
    defaultLanguage?: string;
    languages?: Array<string>;
    countries?: Array<string>;
    currencies?: Array<string>;
    isTest?: boolean;
    title?: string;
    domains?: string[];
    labels?: Label[];
    merchantTypes?: string[];
    deploymentGroupId?: number;
    deploymentGroupRoute?: string;
}

export interface UpdateDataOptions {
    forbidToUpdateIsTest?: boolean;
}

export interface CreateMerchantEntityData extends CreateData {
    code: string;
    type: string;
    params: MerchantParams;
}

export interface EntityFactory {
    createEntity(data: CreateData): Promise<BaseEntity>;

    createMerchant(data: CreateMerchantEntityData): Promise<MerchantInfoWithBalance>;

    createBrand(data: CreateData): Promise<BrandEntity>;

    update(data: UpdateData, options?: UpdateDataOptions): Promise<EntityInfo & WithBalances>;
}

class EntityFactoryImpl implements EntityFactory {
    constructor(public entity: BaseEntity) {
    }

    public async createBrand(data: CreateData): Promise<BrandEntity> {
        data.type = ENTITY_TYPE.BRAND;

        return await db.transaction(async (transaction: Transaction) => {
            return await this.create(data, transaction);
        }) as BrandEntity;
    }

    public async createMerchant(
        data: CreateMerchantEntityData): Promise<MerchantInfoWithBalance> {

        let createdMerchantEntity: BaseEntity;
        let merchant: Merchant;
        await db.transaction(async (transaction: Transaction): Promise<any> => {

            const createMerchantEntityData = Object.assign({}, data, {
                type: ENTITY_TYPE.MERCHANT,
                merchantType: data.type
            });

            createdMerchantEntity = (await this.create(createMerchantEntityData, transaction)) as BaseEntity;
            merchant = await getMerchantCRUDService().create(createdMerchantEntity as BrandEntity, data, transaction);
        });
        const entityInfo: MerchantInfoWithBalance =
            await createdMerchantEntity.toInfoWithBalances() as MerchantInfoWithBalance;
        entityInfo.merchant = merchant.toInfo();
        const settingsService = new EntitySettingsService(createdMerchantEntity);
        await settingsService.patch(DEFAULT_MERCHANT_ENTITY_SETTINGS);
        return entityInfo;
    }

    public async createEntity(data: CreateData): Promise<BaseEntity> {
        return await db.transaction(async (transaction: Transaction) => {
            return await this.create(data, transaction);
        });
    }

    /*
    Create instance of entity & assign jurisdiction.
     */
    private async create(data: CreateData, transaction: Transaction): Promise<BaseEntity> {
        setDefaultLanguageAndTypeIfNeeded(data);

        validateParent(this.entity);
        await this.validateCreateData(data);

        const instance = await this.createInstance(data, transaction);
        await getEntityJurisdictionService().add(instance, data.jurisdictionCode, transaction);
        if (instance.isBrand()) {
            await getAvailableSiteService(instance).create({
                url: data.webSiteUrl,
                status: AVAILABLE_SITE_STATUSES.NORMAL,
                isDefault: true
            }, transaction);
        }

        return instance;
    }

    private async validateCreateData(data: CreateData) {
        if (data.type && !Object.values(ENTITY_TYPE).includes(data.type as ENTITY_TYPE)) {
            throw new Errors.ValidationError("Entity type is not valid");
        }
        if (data.deploymentGroupRoute) {
            const deploymentGroup = await getDeploymentGroupService()
                .getDeploymentGroupByRoute(data.deploymentGroupRoute);

            data.deploymentGroupId = deploymentGroup.id;
        }

        // check that the parent entity is allowed to have this currency
        if (!this.entity.currencyExists(data.defaultCurrency)) {
            throw new Errors.CurrencyNotExistInParentError(data.defaultCurrency);
        }

        // check that the parent entity is allowed to have this language
        if (!this.entity.languageExists(data.defaultLanguage)) {
            throw new Errors.LanguageNotExistInParentError(data.defaultLanguage);
        }

        const dupEntity: Entity = this.entity.find({ name: data.name }) as Entity;
        const isNameExists: boolean = findNameInPath(this.entity.path, data.name);

        if (dupEntity || isNameExists) {
            throw new Errors.EntityAlreadyExistError();
        }

        if (!data.jurisdictionCode) {
            throw new Errors.ValidationError("jurisdictionCode is required for brands and merchants");
        }
        const jurisdiction = await getJurisdictionService().findOne(data.jurisdictionCode);
        const entitySettings = await getEntitySettings(this.entity.path);
        validateCreateDefaultCountry(data, this.entity, jurisdiction, entitySettings);
        validateCreateCountries(data, this.entity, entitySettings);

        this.validateWebSite(data);

        await this.validateDuplicateNamesInSubtree(this.entity, data.name);
        validateDomains(data.domains);

        if (data.merchantTypesInherited) {
            data.merchantTypes = null;
        }
        if (data.type === ENTITY_TYPE.MERCHANT && data.merchantType) {
            const merchantTypes = await this.entity.getMerchantTypes();
            if (!merchantTypes.includes(data.merchantType)) {
                throw new Errors.MerchantTypeIsNotSupportedByParentError(data.merchantType);
            }
        }

        data.merchantType = undefined;
    }

    private async validateDuplicateNamesInSubtree(entity: BaseEntity, name: string) {
        const entitiesSettings = await getEntitiesSettings(entity.path);
        const currentEntityMergedSettings = entitiesSettings[entity.path];

        // if after merging settings current entity has unique flag then find parent which set it
        if (currentEntityMergedSettings.uniqueEntityNamesInSubtree && name) {
            const entityPathWithUniqueSetting: string = Object.keys(entitiesSettings)
                .find(entityName => entitiesSettings[entityName] &&
                    entitiesSettings[entityName].uniqueEntityNamesInSubtree);

            const entityWithUniqueSetting = await EntityCache.findOne<Entity>({
                path: entityPathWithUniqueSetting
            });

            if (!entityWithUniqueSetting) {
                return;
            }

            const duplicate = entityWithUniqueSetting.find({ name });
            if (duplicate) {
                throw new Errors.EntityAlreadyExistError();
            }
        }
    }

    private async createInstance(data: CreateData, transaction?: Transaction): Promise<BaseEntity> {
        const newEntity: ChildEntity = createEntityByType(true, data.type);
        newEntity.name = data.name;

        if (data.deploymentGroupId) {
            newEntity.deploymentGroupId = data.deploymentGroupId;
        }

        newEntity.setParent(this.entity as Entity);
        (this.entity as Entity).child.push(newEntity);

        newEntity.type = data.type;
        newEntity.status = "normal";
        if (newEntity.isBrand() || (newEntity as BrandEntity).isMerchant) {
            newEntity.status = await defineDefaultStatusForNewEntity(newEntity);
        }
        newEntity.description = data.description;

        if (data.title) {
            newEntity.title = data.title;
        }

        if (data.key) {
            newEntity.key = data.key;
        }

        if (data.countries) {
            if (Array.isArray(data.countries)) {
                for (const country of data.countries) {
                    newEntity.addCountry(country);
                }
            } else {
                return Promise.reject(new Errors.CountriesIsNotArray());
            }
        }

        if (data.defaultCountry) {
            newEntity.setDefaultCountry(data.defaultCountry);
            newEntity.addCountry(data.defaultCountry);
        }

        if (data.currencies) {
            if (Array.isArray(data.currencies)) {
                for (const currency of data.currencies) {
                    newEntity.addCurrency(currency);
                }
            } else {
                return Promise.reject(new Errors.CurrenciesIsNotArray());
            }
        }

        newEntity.addCurrency(data.defaultCurrency);
        newEntity.setDefaultCurrency(data.defaultCurrency);

        if (data.languages) {
            if (Array.isArray(data.languages)) {
                for (const language of data.languages) {
                    newEntity.addLanguage(language);
                }
            } else {
                return Promise.reject(new Errors.LanguagesIsNotArray());
            }
        } else {
            newEntity.addLanguage(data.defaultLanguage);
        }

        newEntity.setDefaultLanguage(data.defaultLanguage);

        if (data.merchantTypes && !newEntity.isBrand()) {
            if (Array.isArray(data.merchantTypes)) {
                await newEntity.addMerchantType(data.merchantTypes);
            } else {
                return Promise.reject(new Errors.MerchantTypesIsNotArray());
            }
        }

        if (data.domains) {
            newEntity.domains = sanitizeDomains(data.domains);
        }

        if (newEntity.isBrand()) {
            (newEntity as BrandEntity).isMerchant = data.type === ENTITY_TYPE.MERCHANT;
        }

        (newEntity as BrandEntity).isTest = this.entity.inheritedTestMode || data.isTest || false;

        const entityItem = await EntityModel.create(newEntity, { transaction });
        newEntity.id = entityItem.id;
        newEntity.key = entityItem.key;

        return newEntity;
    }

    public async update(data: UpdateData, options?: UpdateDataOptions): Promise<EntityInfo & WithBalances> {
        const child = this.entity as ChildEntity;

        const parent = child.getParent();
        validateParent(parent);
        await this.validateUpdateData(parent, data);
        const entity = await this.patchEntity(data, options);
        return entity.toInfoWithBalances();
    }

    private async validateUpdateData(parent: BaseEntity, data: UpdateData) {
        if (data.deploymentGroupRoute) {
            const deploymentGroup = await getDeploymentGroupService()
                .getDeploymentGroupByRoute(data.deploymentGroupRoute);

            data.deploymentGroupId = deploymentGroup.id;
        }

        validateEntityStatus(this.entity);
        const settings = await getEntitySettings(this.entity.path);
        const [jurisdiction] = await getEntityJurisdictionService().findAll({ entityId: this.entity.id });
        validateUpdateDefaultCountry(data, parent, this.entity as ChildEntity, jurisdiction, settings);
        validateUpdateCountries(data, parent, this.entity as ChildEntity, settings);
        this.validateLanguage(parent, data);
        await this.validateCurrency(parent, data);
        validateDomains(data.domains);
    }

    private validateLanguage(parent: BaseEntity, data: UpdateData) {
        const entity = this.entity as ChildEntity;

        if (data.defaultLanguage) {
            // check that the parent entity is allowed to have this language
            if (!parent.languageExists(data.defaultLanguage)) {
                throw new Errors.LanguageNotExistInParentError(data.defaultLanguage);
            }
        }

        if (data.languages) {
            if (!Array.isArray(data.languages)) {
                throw new Errors.LanguagesIsNotArray();
            }

            for (const code of data.languages) {
                if (!LANGUAGES[code]) {
                    throw new Errors.LanguageNotFoundError(code);
                }

                // parent does not allowed this language code
                if (!parent.languageExists(code)) {
                    throw new Errors.LanguageNotExistInParentError(code);
                }
            }

            const removedLanguages = this.entity.getLanguages()
                .filter(code => !data.languages.includes(code));

            for (const code of removedLanguages) {
                const children = ((this.entity as Entity).child || []).filter(child => child.languageExists(code));
                if (children.length) {
                    throw new Errors.ValidationError(
                        `This language code ${code} in use in child entities. You cannot remove it.`,
                        undefined,
                        {
                            children: children.map(child => ({
                                title: child.title,
                                path: child.path,
                                name: child.name
                            }))
                        });
                }
            }

            const defaultLanguage = data.defaultLanguage || entity.defaultLanguage;

            if (data.languages.indexOf(defaultLanguage) < 0) {
                throw new Errors.LanguageNotInListError(defaultLanguage);
            }
        }
    }

    private async validateCurrency(parent: BaseEntity, data: UpdateData) {
        const entity = this.entity as ChildEntity;
        const { social } = await getEntitySettings(entity.path);

        if (data.defaultCurrency) {
            if (!parent.currencyExists(data.defaultCurrency)) {
                throw new Errors.CurrencyNotExistInParentError(data.defaultCurrency);
            }
            if (!Currencies.value(data.defaultCurrency)?.isSocial && social) {
                throw new Errors.CurrencyNotSocialError(data.defaultCurrency);
            }
            if (Currencies.value(data.defaultCurrency)?.isSocial && !social) {
                throw new Errors.SocialCurrencyNotAllowedError(data.defaultCurrency);
            }
        }

        if (data.currencies) {
            if (!Array.isArray(data.currencies)) {
                throw new Errors.ValidationError("Currencies should be a list");
            }
            for (const code of data.currencies) {
                if (!parent.currencyExists(code)) {
                    throw new Errors.CurrencyNotExistInParentError(code);
                }
                if (!Currencies.exists(code)) {
                    throw new Errors.CurrencyNotFoundError(code);
                }
                if (!Currencies.value(code)?.isSocial && social) {
                    throw new Errors.CurrencyNotSocialError(code);
                }
                if (Currencies.value(code)?.isSocial && !social) {
                    throw new Errors.SocialCurrencyNotAllowedError(code);
                }
            }

            const removedCurrencies = this.entity.getCurrencies()
                .filter(code => !data.currencies.includes(code));
            for (const code of removedCurrencies) {
                const children = ((this.entity as Entity).child || []).filter(child => child.currencyExists(code));
                if (children.length) {
                    throw new Errors.ValidationError(
                        `This currency code ${code} in use in child entities. You cannot remove it.`,
                        undefined,
                        {
                            children: children.map(child => ({
                                title: child.title,
                                path: child.path,
                                name: child.name
                            }))
                        });
                }
            }

            const defaultCurrency = data.defaultCurrency || entity.defaultCurrency;
            if (data.currencies.indexOf(defaultCurrency) < 0) {
                throw new Errors.CurrencyNotInListError(defaultCurrency);
            }
        }
    }

    private async patchEntity(data: UpdateData, options: UpdateDataOptions = {}): Promise<BaseEntity> {
        if (data.deploymentGroupId) {
            this.entity.deploymentGroupId = data.deploymentGroupId;
        }
        if (data.title) {
            this.entity.title = data.title;
        }

        if (data.status) {
            if (data.status === EntityStatus.TEST) {
                await validateEntityTestStatus(this.entity);
            }
            this.entity.status = data.status;
        }

        if (data.description) {
            this.entity.description = data.description;
        }

        this.updateIsTest(data, options);

        if (data.domains) {
            this.entity.domains = sanitizeDomains(data.domains);
        }

        const child = this.entity as ChildEntity;
        if (data.countries) {
            if (!Array.isArray(data.countries)) {
                throw new Errors.CountriesIsNotArray();
            }
            for (const code of data.countries) {
                child.addCountry(code);
            }
        }

        if (data.defaultCountry !== undefined) {
            child.setDefaultCountry(data.defaultCountry);
        }

        if (data.currencies) {
            child.updateCurrencyArray(data.currencies);
        }

        if (data.defaultCurrency) {
            child.setDefaultCurrency(data.defaultCurrency);
        }

        if (data.languages) {
            child.updateLanguageArray(data.languages);
        }

        if (data.defaultLanguage) {
            child.setDefaultLanguage(data.defaultLanguage);
        }

        if (data.merchantTypes) {
            await child.updateMerchantTypesArray(data.merchantTypes);
        }

        try {
            await child.save();
        } catch (err) {
            if (err instanceof UniqueConstraintError) {
                throw new Errors.EntityAlreadyExistError();
            }

            throw err;
        }

        return child;
    }

    private updateIsTest(data: UpdateData, options: UpdateDataOptions) {
        if (options.forbidToUpdateIsTest) {
            return;
        }

        (this.entity as BrandEntity).isTest =
            (this.entity as BrandEntity).getParent().inheritedTestMode
                ? true
                : typeof data.isTest === "boolean"
                    ? data.isTest
                    : this.entity.isTest;
    }

    private validateWebSite(data: CreateData): void {
        if (!data.webSiteUrl && (data.type === ENTITY_TYPE.MERCHANT || data.type === ENTITY_TYPE.BRAND)) {
            throw new Errors.ValidationError("webSiteUrl is required for brands and merchants");
        }
    }
}

export default function getEntityFactory(entity: BaseEntity): EntityFactory {
    return new EntityFactoryImpl(entity);
}

function validateParent(parent: BaseEntity, allowBrand: boolean = false) {
    if (!parent) {
        throw new Errors.ParentNotFoundError();
    }

    if (parent.isSuspended()) {
        throw new Errors.ParentSuspendedError();
    }

    if (parent.isBrand() && !allowBrand) {
        throw new Errors.ParentIsBrand();
    }
}

function validateDomains(domains: string[]): void {

    if (domains === undefined) {
        return;
    }

    if (!Array.isArray(domains)) {
        throw new Errors.ValidationError("Domains should be array of string");
    }

    const invalidDomains = [];
    for (const domain of domains) {
        if (typeof domain !== "string" || !DOMAIN_PATTERN.test(domain)) {
            invalidDomains.push(domain);
        }
    }

    if (invalidDomains.length) {
        throw new Errors.ValidationError(`Invalid domains - ${invalidDomains}`);
    }

}

function setDefaultLanguageAndTypeIfNeeded(data: CreateData) {
    if (!data.defaultLanguage) {
        data.defaultLanguage = "en";
    }

    if (!data.type) {
        data.type = ENTITY_TYPE.ENTITY;
    }
}

function findNameInPath(path: string, name: string): boolean {
    if (path && name) {
        const parts: string[] = path.split(":");
        for (let i = 0, length = parts.length; i < length; i++) {
            if (parts[i] === name) {
                return true;
            }
        }
    }

    return false;
}

function sanitizeDomains(domains: string[]): string[] {
    return [...new Set(domains)];
}
