import {
    DestroyOptions,
    ForeignKeyConstraintError,
    Op,
    Transaction,
    UniqueConstraintError,
    WhereOptions,
} from "sequelize";
import * as Errors from "../errors";
import { RoleNotExist } from "../errors";
import { DetailedRoleInfo, ExtendedRoleInfo, Role, RoleInfo } from "../entities/role";
import { RoleDBInstance, UserRoleDBInstance } from "../models/role";
import { sequelize as db } from "../storage/db";
import logger from "../utils/logger";
import { checkPermissions } from "./permission";
import { PermissionsList } from "../entities/user";
import { BaseEntity } from "../entities/entity";
import * as EntityService from "../services/entity";
import { getChildIds, getParentIds } from "./entity";
import EntityCache from "../cache/entity";
import * as RolesCache from "../cache/role";
import { Models } from "../models/models";
import { difference } from "lodash";
import { decodeId, encodeId } from "../utils/publicid";

const log = logger();
const RoleModel = Models.RoleModel;
const UserRoleModel = Models.UserRoleModel;

export const queryParamsKeys = ["title"];

export interface CreateData {
    entityId: number;
    title: string;
    description?: string;
    permissions: Array<string>;
    isShared: boolean;
}

export interface UpdateData {
    title?: string;
    description?: string;
    permissions?: Array<string>;
    isShared?: boolean;
    pathTo?: string;
}

export class RoleImpl implements Role {

    public id: number;
    public title: string;
    public description: string;
    public permissions: PermissionsList;
    public entityId: number;
    public isShared: boolean;
    public owned: boolean;
    public ownedBy: string;

    constructor(item?: RoleDBInstance, owned?: boolean, ownedBy?: string) {
        if (!item) {
            return;
        }

        this.id = item.get("id");
        this.title = item.get("title");
        this.permissions = item.get("permissions").sort();
        this.entityId = item.get("entityId");
        this.isShared = item.get("isShared");

        if (item.get("description")) {
            this.description = item.get("description");
        }

        if (owned !== undefined) {
            this.owned = owned;
        }

        if (ownedBy !== undefined) {
            this.ownedBy = ownedBy;
        }
    }

    public toInfo(): RoleInfo {
        const info: RoleInfo = {
            id: this.id,
            title: this.title,
            owned: this.owned,
            ownedBy: this.ownedBy
        };
        if (this.description) {
            info.description = this.description;
        }
        return info;
    }

    public toEncodedInfo(): RoleInfo {
        const { id, ...info } = this.toInfo();
        return {
            ...info,
            id: encodeId(this.id),
        };
    }

    public toExtendedInfo(): ExtendedRoleInfo {
        return {
            ...this.toInfo(),
            isShared: this.isShared
        };
    }

    public toDetailedInfo(): DetailedRoleInfo {
        return {
            ...this.toExtendedInfo(),
            permissions: this.permissions.sort()
        };
    }
}

export async function createRole(data: CreateData): Promise<Role> {
    const record: RoleImpl = new RoleImpl();

    record.entityId = data.entityId;
    record.title = data.title;
    if (data.description) {
        record.description = data.description;
    }

    await checkPermissions(data.permissions);
    data.permissions.sort();
    record.permissions = data.permissions;
    record.isShared = data.isShared;

    try {
        const item: RoleDBInstance = await RoleModel.create(record);
        return new RoleImpl(item);
    } catch (err) {
        return Promise.reject(new Errors.RoleNotCreated());
    }
}

export async function updateRole(roleId: number, entity: BaseEntity, data: UpdateData): Promise<Role> {
    const role: Role = await findOne(roleId, entity);

    if (data.title) {
        role.title = data.title;
    }

    if (data.description) {
        role.description = data.description;
    }

    if (data.permissions) {
        await checkPermissions(data.permissions);
        data.permissions.sort();
        role.permissions = data.permissions;
    }

    if (data.isShared !== undefined) {
        role.isShared = data.isShared;
    }

    if (data.pathTo) {
        const entityTo = await EntityCache.findOne({ path: getEntityPath(data.pathTo) },
            ":", true) as BaseEntity;
        if (entityTo && entityTo.isSuspended()) {
            return Promise.reject(new Errors.ParentSuspendedError());
        }
        role.entityId = entityTo.id;
    }

    try {
        await RoleModel.update(role, { where: { id: roleId } });
    } catch (err) {
        return Promise.reject(new Errors.RoleUpdateFail());
    }

    RolesCache.reset(String(roleId));

    return role;
}

function getEntityPath(aPath: string): string {
    let path: string = aPath.trim();
    if (!path.startsWith(EntityService.PATH_CHAR)) {
        path = EntityService.PATH_CHAR + path;
    }
    if (!path.endsWith(EntityService.PATH_CHAR)) {
        path += EntityService.PATH_CHAR;
    }
    return path;
}

export async function removeRole(roleId: number, entity: BaseEntity, force: boolean): Promise<number> {

    const role: Role = await findOne(roleId, entity);
    let count: number;

    if (force) {
        await db.transaction(async (transaction: Transaction): Promise<any> => {
            await removeRoleFromUser(role, transaction);
            const entityId = entity.id;
            log.warn({ role, entityId }, "Role force delete without deleting linked records from usersRecords");
            count = await destroyRoleModel(role, entity.id, transaction);
            return;
        });
    } else {
        try {
            count = await destroyRoleModel(role, entity.id);
        } catch (err) {
            if (err instanceof ForeignKeyConstraintError) {
                return Promise.reject(new Errors.FailedToDeleteRoleNeedForce());
            }
        }
    }

    RolesCache.reset(String(roleId));

    return count;
}

async function removeRoleFromUser(role: Role, transaction?: Transaction): Promise<void> {
    const destroyOptions: DestroyOptions = {
        where: {
            roleId: role.id,
        },
    };
    if (transaction) {
        destroyOptions["transaction"] = transaction;
    }

    await UserRoleModel.destroy(destroyOptions);
}

async function destroyRoleModel(role: Role, entityId: number, transaction?: Transaction): Promise<number> {
    const destroyOptions: DestroyOptions = {
        where: {
            id: role.id,
            entityId: entityId,
        },
    };
    if (transaction) {
        destroyOptions["transaction"] = transaction;
    }

    const count = await RoleModel.destroy(destroyOptions);

    if (count === 0) {
        return Promise.reject(new Errors.RoleNotExist());
    }

    return count;
}

export async function listRoles(entity: BaseEntity, query?: WhereOptions<any>): Promise<ExtendedRoleInfo[]> {
    const ownRoles = await findRoles(entity, { ...query, entityId: entity.id });
    const parentRoles = await getParentRoles(entity, query);
    const childRoles = await getChildRoles(entity, query);

    return toExtendedInfo(...ownRoles, ...parentRoles, ...childRoles);
}

export async function listChildrenRoles(entity: BaseEntity, query?: WhereOptions<any>): Promise<ExtendedRoleInfo[]> {
    const ownRoles = await findRoles(entity, { ...query, entityId: entity.id });
    const childRoles = await getChildRoles(entity, query);

    return toExtendedInfo(...ownRoles, ...childRoles);
}

function toExtendedInfo(...roles: Role[]): ExtendedRoleInfo[] {
    const rolesInfo = roles.map((role: Role) => role.toExtendedInfo());

    rolesInfo.sort((a, b) => a.title.localeCompare(b.title));

    return rolesInfo;
}

export async function findRoles(ownerEntity: BaseEntity, query: WhereOptions<any> = {}): Promise<Role[]> {

    const roles = await RoleModel.findAll({
        where: query,
        include: [
            {
                model: Models.EntityModel,
                as: "entity"
            }
        ]

    });

    return roles.map(item => {
        const itemEntity: BaseEntity = item.get("entity");
        const isOwner = itemEntity.id === ownerEntity.id;
        return new RoleImpl(item, isOwner, itemEntity.name);
    });
}

export async function getParentRoles(entity: BaseEntity, query?: WhereOptions<any>): Promise<Role[]> {

    const parentEntityIds = getParentIds(entity);
    if (!parentEntityIds.length) {
        return [];
    }

    return findRoles(entity, { ...query, isShared: true, entityId: { [Op.in]: parentEntityIds } });
}

export async function getChildRoles(entity: BaseEntity, query?: WhereOptions<any>): Promise<Role[]> {
    const childEntityIds = getChildIds(entity);

    if (!childEntityIds.length) {
        return [];
    }

    return findRoles(entity, { ...query, entityId: { [Op.in]: childEntityIds } });
}

export async function getRoleFromEntity(roleId: number, entity: BaseEntity, editorEntity?: BaseEntity): Promise<Role> {
    // check shared Roles
    const parentRoles = await getParentRoles(entity, { id: roleId });
    if (parentRoles.length) {
        return parentRoles[0];
    }

    const role = await findOne(roleId, editorEntity ? editorEntity : entity);
    if (role) {
        return role;
    }

    return Promise.reject(new Errors.RoleManageFailed());
}

export async function findOne(roleId: number, entity: BaseEntity, transaction?: Transaction): Promise<Role> {
    const item: RoleDBInstance = await RoleModel.findOne({
        where: {
            id: roleId,
        },
        transaction: transaction,
    });

    if (!item) {
        return Promise.reject(new Errors.RoleNotExist());
    }

    const role: RoleImpl = new RoleImpl(item);

    // User can manage only own Roles or Roles of its children
    if (!entity.find({ id: role.entityId })) {
        return Promise.reject(new Errors.RoleManageFailed());
    }

    return role;
}

function parseRoleId(roleInfo) {
    return roleInfo.id ? parseInt(decodeId(roleInfo.id) || roleInfo.id, 10) : undefined;
}

export async function addRolesToUser(entity: BaseEntity,
                                     userId: number,
                                     rolesToAdd: RoleInfo[],
                                     editorEntity: BaseEntity,
                                     removeOld: boolean,
                                     transaction: Transaction): Promise<void> {

    if (!rolesToAdd || !Array.isArray(rolesToAdd)) {
        return Promise.reject(new Errors.RoleAddToUserError());
    }

    const currentRolesIds = (
        await UserRoleModel.findAll({
            where: { userId }
        })
    ).map((userRole: UserRoleDBInstance) => userRole.get("roleId"));

    const editorEntityRoles = editorEntity ? await listRoles(editorEntity) : undefined;

    const editorEntityRoleIds = editorEntityRoles ? editorEntityRoles.map((role: RoleInfo) => role.id) : [];
    const untouchedRoleIds = editorEntityRoles ? difference(currentRolesIds, editorEntityRoleIds) : [];

    if (removeOld) {
       await removeUserRoles(userId, untouchedRoleIds, transaction);
    }

    const promises = rolesToAdd
        .filter(roleToAdd => !untouchedRoleIds.includes(parseRoleId(roleToAdd)))
        .map(async (roleInfo) => {
            try {
                // TODO Add opportunity for multiple adding Roles to User
                if (!roleInfo.id) {
                    return Promise.reject(new Errors.RoleAddToUserError("roleInfo.id is absent"));
                }
                const roleId: number = parseRoleId(roleInfo);
                if (!roleId) {
                    return Promise.reject(new Errors.RoleAddToUserError("role id is incorrect"));
                }
                const role: Role = await getRoleFromEntity(roleId, entity, editorEntity);
                await UserRoleModel.create({
                    userId: userId,
                    roleId: role.id,
                }, { transaction });
            } catch (err) {
                if (err instanceof UniqueConstraintError) {
                    return Promise.reject(new Errors.RoleAddToUserError());
                }
                if (err instanceof RoleNotExist) {
                    return Promise.reject(new Errors.RoleAddToUserError("roleInfo.id is wrong"));
                }
                return Promise.reject(err);
            }
        });

    await Promise.all(promises);
}

/**
 * @param untouchedRoleIds - ids of roles that shall not be removed
 */
async function removeUserRoles(userId: number, untouchedRoleIds: number[], transaction: Transaction): Promise<void> {
    const destroyOptions: DestroyOptions = untouchedRoleIds.length ?
        {
            where: {
                userId,
                roleId: { [Op.notIn]: untouchedRoleIds },
            }
        } :
        {
            where: {
                userId,
            }
        };
    destroyOptions.transaction = transaction;
    await UserRoleModel.destroy(destroyOptions);
}

export function getPermissions(roles: Role[]): PermissionsList {
    return [...new Set((roles.reduce((prev, role) => prev.concat(role.permissions), [])))];
}
