import { ExpireGameRequest } from "../entities/expireGame";
import { EntityCouldNotBeFound, NotBrand } from "../errors";
import EntityCache from "../cache/entity";
import { measures } from "@skywind-group/sw-utils";
import { createFinalizeService } from "../history/unfinishedRoundFinalizeService";
import measure = measures.measure;
import { BrandEntity } from "../entities/brand";

export class ExpireGameService {

    @measure({ name: "ExpireGameService.expire" })
    public async expire(request: ExpireGameRequest) {
        const brand = await EntityCache.findOne<BrandEntity>({ id: request.brandId });
        if (!brand) {
            throw new EntityCouldNotBeFound();
        }
        if (!brand.isBrand()) {
            throw new NotBrand();
        }
        const service = createFinalizeService(brand, undefined);
        await service.finalize({
            gameContextId: request.gameContextId,
            gameCode: request.gameCode,
            roundId: request.roundId
        });
    }
}

export default new ExpireGameService();
