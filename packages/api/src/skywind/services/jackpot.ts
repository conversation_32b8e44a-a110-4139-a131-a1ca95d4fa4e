import { EntityGameInfo, JackpotMapping } from "../entities/game";
import * as TokenUtils from "../utils/token";
import { isEmpty } from "lodash";
import { getJPNServer } from "./jpnserver";
import { JackpotConfigurationLevel, JackpotInstance, JackpotTickerMapping, JackpotType } from "../entities/jackpot";
import * as Errors from "../errors";
import { ValidationError } from "../errors";
import { BaseEntity } from "../entities/entity";
import { getEntityJurisdictionService } from "./entityJurisdiction";
import { getChildIds, getParentIds } from "./entity";
import { getEntitySettings } from "./settings";
import { JackpotFilterBuilder, JackpotFilterParams } from "../utils/jackpotFilterBuilder";
import { EntitySettings } from "../entities/settings";

export async function validateJackpotsExist(jackpotIds: string[], entitySettings: EntitySettings): Promise<void> {
    if (isEmpty(jackpotIds)) {
        return;
    }

    const token = await getJackpotAuthToken();
    const { jpnUrl, jpnTickerUrl } = entitySettings;
    const JPNServer = getJPNServer(jpnUrl, jpnTickerUrl);

    const jackpots: JackpotInstance[] = await JPNServer.getJackpots(jackpotIds, token);
    const notFound = jackpotIds.filter((id) => !jackpots.find((jackpot) => jackpot.id === id));
    if (notFound.length) {
        return Promise.reject(new ValidationError(`marketing jackpots ${notFound} not exist`));
    }
}

export async function validateJackpotIds(
    jackpotMapping: JackpotMapping,
    entity?: BaseEntity,
    checkMatchingJackpotIds = true
) {
    const jackpotIds = getFlattedJackpotIds(jackpotMapping);
    if (isEmpty(jackpotIds)) {
        return;
    }

    const token = await getJackpotAuthToken();
    const { jpnUrl, jpnTickerUrl } = entity ? await getEntitySettings(entity.path) : {} as EntitySettings;
    const JPNServer = getJPNServer(jpnUrl, jpnTickerUrl);
    let jackpots: JackpotInstance[];
    try {
        // if isExternalJackpotSupported JPNServer can fail solving getJackpots
        // for example on PP side we have template jackpot ids: {siteCode}_widget-test_{currency} and siteCode can not
        // be resolved at master entity level
        jackpots = await JPNServer.getJackpots(jackpotIds, token);
    } catch (e) {
        if (checkMatchingJackpotIds) {
            throw (e);
        }
        return; // in case JPNServer can not get EGP Jackpots skip validation
    }
    const errorMessages = [];

    for (const jackpotType of Object.keys(jackpotMapping)) {
        const jackpotId = jackpotMapping[jackpotType];
        const filteredJackpots = jackpots.filter(jackpot => jackpot.id === jackpotId);

        if (!filteredJackpots.length) {
            if (checkMatchingJackpotIds) {
                errorMessages.push(`jackpotId ${jackpotId} is not found`);
            }
            continue;
        }

        const jackpotInstance = filteredJackpots[0];
        if (jackpotInstance.type !== jackpotType) {
            errorMessages.push(`${jackpotId} does not belong to ${jackpotType} type`);
        }
    }

    if (errorMessages.length) {
        return Promise.reject(new ValidationError(errorMessages.join()));
    }

    for (const jp of jackpots) {
        await validateJackpotConfigurationLevel(jp, entity);
    }
}

function validateJackpotConfigurationLevel(jackpotInstance: JackpotInstance, entity: BaseEntity): Promise<void> {
    switch (jackpotInstance.jackpotConfigurationLevel) {
        case JackpotConfigurationLevel.SPECIFIC_BRAND_ONLY:
            return handleSpecificBrandOnlyValidation(jackpotInstance, entity);
        case JackpotConfigurationLevel.SHARED_BETWEEN_ONE_OPERATOR_FOR_ONE_JURISDICTION:
            return handleOneOperatorOneJurisdictionValidation(jackpotInstance, entity);
        case JackpotConfigurationLevel.SHARED_BETWEEN_SEVERAL_OPERATORS_FOR_ONE_JURISDICTION:
            return handleSeveralOperatorsOneJurisdictionValidation(jackpotInstance, entity);
        case JackpotConfigurationLevel.SHARED_BETWEEN_SEVERAL_JURISDICTIONS_FOR_ONE_OPERATOR:
            return handleSeveralJurisdictionsOneOperatorValidation(jackpotInstance, entity);
        case JackpotConfigurationLevel.SHARED_BETWEEN_SEVERAL_OPERATORS:
        case JackpotConfigurationLevel.SHARED_GLOBALLY:
            // No validation required
            return;
    }
}

async function handleSeveralJurisdictionsOneOperatorValidation(jackpotInstance: JackpotInstance,
                                                               entity: BaseEntity): Promise<void> {
    if (![...getParentIds(entity), entity.id].includes(jackpotInstance.entityId)) {
        throw new Errors.ValidationJackpotConfigurationEntityMismatchError(entity.id, jackpotInstance.entityId);
    }
    if (entity.isBrand()) {
        const jurisdiction = await getEntityJurisdictionService().findOneByEntityId(entity.id);
        if (jackpotInstance.jackpotConfigurationLevel > jurisdiction.allowedJackpotConfigurationLevel) {
            throw new Errors.ValidationJackpotConfigurationError(jackpotInstance.jackpotConfigurationLevel,
                jurisdiction.allowedJackpotConfigurationLevel, jurisdiction.code);
        }
    } else {
        const jurisdictions = await getEntityJurisdictionService().findAll({ entityId: entity.id });
        let highestLevel: number = 0;
        let highestLevelJurisdictionCode: string;
        for (const jurisdiction of jurisdictions) {
            if (jurisdiction.allowedJackpotConfigurationLevel > highestLevel) {
                highestLevel = jurisdiction.allowedJackpotConfigurationLevel;
                highestLevelJurisdictionCode = jurisdiction.code;
            }
        }
        if (jackpotInstance.jackpotConfigurationLevel > highestLevel) {
            throw new Errors.ValidationJackpotConfigurationError(jackpotInstance.jackpotConfigurationLevel,
                highestLevel,
                highestLevelJurisdictionCode);
        }
    }
}

async function handleSeveralOperatorsOneJurisdictionValidation(jackpotInstance: JackpotInstance,
                                                               entity: BaseEntity): Promise<void> {
    const entityJurisdictions = await getEntityJurisdictionService().findAll({ entityId: entity.id });
    const foundJurisdiction = entityJurisdictions.find(jrsd => jrsd.code === jackpotInstance.jurisdictionCode);
    if (!foundJurisdiction) {
        throw new Errors.ValidationError(
            `Jurisdiction ${jackpotInstance.jurisdictionCode} is not available for entity ${entity.name} (id = ${entity.id})`
        );
    }
    if (jackpotInstance.jackpotConfigurationLevel > foundJurisdiction.allowedJackpotConfigurationLevel) {
        throw new Errors.ValidationJackpotConfigurationError(jackpotInstance.jackpotConfigurationLevel,
            foundJurisdiction.allowedJackpotConfigurationLevel, foundJurisdiction.code);
    }
}

async function handleOneOperatorOneJurisdictionValidation(jackpotInstance: JackpotInstance,
                                                          entity: BaseEntity): Promise<void> {
    if (![...getParentIds(entity), entity.id].includes(jackpotInstance.entityId)) {
        throw new Errors.ValidationJackpotConfigurationEntityMismatchError(entity.id, jackpotInstance.entityId);
    }
    if (entity.isBrand()) {
        const jurisdiction = await getEntityJurisdictionService().findOneByEntityId(entity.id);
        if (jurisdiction.code !== jackpotInstance.jurisdictionCode) {
            throw new Errors.ValidationJackpotConfigurationJurisdictionMismatchError(
                jurisdiction.code,
                jackpotInstance.jurisdictionCode
            );
        }
        if (jackpotInstance.jackpotConfigurationLevel > jurisdiction.allowedJackpotConfigurationLevel) {
            throw new Errors.ValidationJackpotConfigurationError(jackpotInstance.jackpotConfigurationLevel,
                jurisdiction.allowedJackpotConfigurationLevel,
                jurisdiction.code);
        }
    } else {
        const jurisdictions = await getEntityJurisdictionService().findAll({ entityId: entity.id });
        const jurisdictionCodes = jurisdictions.map(j => j.code);
        if (!jurisdictionCodes.includes(jackpotInstance.jurisdictionCode)) {
            throw new Errors.ValidationJackpotConfigurationJurisdictionMismatchError(
                jurisdictionCodes.join(","),
                jackpotInstance.jurisdictionCode
            );
        }
        let highestLevel: number = 0;
        let highestLevelJurisdictionCode: string;
        for (const jurisdiction of jurisdictions) {
            if (jurisdiction.allowedJackpotConfigurationLevel > highestLevel) {
                highestLevel = jurisdiction.allowedJackpotConfigurationLevel;
                highestLevelJurisdictionCode = jurisdiction.code;
            }
        }
        if (jackpotInstance.jackpotConfigurationLevel > highestLevel) {
            throw new Errors.ValidationJackpotConfigurationError(jackpotInstance.jackpotConfigurationLevel,
                highestLevel,
                highestLevelJurisdictionCode);
        }
    }
}

async function handleSpecificBrandOnlyValidation(jackpotInstance: JackpotInstance, entity: BaseEntity): Promise<void> {
    if (entity.isBrand()) {
        if (jackpotInstance.entityId !== entity.id) {
            throw new Errors.ValidationJackpotConfigurationEntityMismatchError(entity.id, jackpotInstance.entityId);
        }
    } else if (!getChildIds(entity).includes(jackpotInstance.entityId)) {
        throw new Errors.ValidationJackpotConfigurationEntityMismatchError(entity.id, jackpotInstance.entityId);
    }
    const jurisdiction = await getEntityJurisdictionService().findOneByEntityId(entity.id);
    if (jackpotInstance.jackpotConfigurationLevel > jurisdiction.allowedJackpotConfigurationLevel) {
        throw new Errors.ValidationJackpotConfigurationError(jackpotInstance.jackpotConfigurationLevel,
            jurisdiction.allowedJackpotConfigurationLevel, jurisdiction.code);
    }
}

export async function validateJackpotTypes(types: string[], isExternalJackpotSupported?: boolean) {
    if (isEmpty(types)) {
        return;
    }

    const token = await getJackpotAuthToken();
    const JPNServer = getJPNServer();

    const jackpotTypes: JackpotType[] = await JPNServer.getJackpotTypes(types, token);
    const errorMessages = [];

    for (const type of types) {
        if (!jackpotTypes.some(jackpot => jackpot.name === type)) {
            // If external jackpots, consider unknown jackpot types as valid
            if (!isExternalJackpotSupported) {
                errorMessages.push(`jackpotType ${type} is not found`);
            }
        }
    }

    if (errorMessages.length) {
        return Promise.reject(new ValidationError(errorMessages.join()));
    }

}

export async function appendJackpots(entity: BaseEntity, gamesList: EntityGameInfo[], currency?: string) {
    const jackpotIds = []
        .concat
        .apply([],
            gamesList.map(game => game.settings && getFlattedJackpotIds(game.settings.jackpotId)))
        .filter(self => self);
    const jackpotTickerMapping = await getJackpotTickerMapping(entity, jackpotIds, currency);

    const entitySettings = await getEntitySettings(entity.path);

    for (const game of gamesList) {
        if (!game.settings) {
            continue;
        }
        const jackpots = getFlattedJackpotIds(game.settings.jackpotId);
        if (!jackpots) {
            continue;
        }
        game.jackpots = {};
        for (const jpId of jackpots) {
            const mappedJpId = mapJackpotId(entitySettings, jpId, currency);
            game.jackpots[mappedJpId] = jackpotTickerMapping[mappedJpId];
        }
    }
}

/**
 * Create array of jackpotIds
 * This is function for back compatibility because jackpotId setting might be an object, array and string
 * @param {JackpotMapping} jackpots
 * @returns {any}
 */
function getFlattedJackpotIds(jackpots: JackpotMapping) {
    if (isEmpty(jackpots)) {
        return;
    }
    if ((typeof jackpots) === "object") {
        if (Array.isArray(jackpots)) {
            return jackpots;
        } else {
            const jackpotIds = [];
            for (const jackpotType of Object.keys(jackpots)) {
                jackpotIds.push(jackpots[jackpotType]);
            }
            return jackpotIds;
        }
    } else {
        return [jackpots];
    }

}

async function getJackpotTickerMapping(entity: BaseEntity, jackpotIds: string[], currency: string):
    Promise<JackpotTickerMapping> {
    if (!jackpotIds || !jackpotIds.length) {
        return {};
    }

    const entitySettings = await getEntitySettings(entity.path);

    const mappedJackpotIds = jackpotIds.map(jpId => mapJackpotId(entitySettings, jpId, currency));
    const { jpnUrl, jpnTickerUrl } = entitySettings;
    const JPNServer = getJPNServer(jpnUrl, jpnTickerUrl);

    const tickers = await JPNServer.getJackpotTickers(mappedJackpotIds, currency);
    return tickers.reduce((result, ticker) => {
        result[ticker.jackpotId] = {
            id: ticker.jackpotId,
            currency: ticker.currency || currency,
            pools: ticker.pools
        };
        return result;
    }, {});
}

export function mapJackpotId(
    entitySettings: EntitySettings,
    jackpotId: string,
    currency: string
): string {
    const jackpotMatch = jackpotId.match(/(\w+);(.*?);(.*)/);
    if (jackpotMatch !== null) {
        const jackpotFilterParams: JackpotFilterParams = {
            currency
        };
        const gameProviderCode = jackpotMatch[1];
        const jackpotId = jackpotMatch[2];
        const filterTemplate = jackpotMatch[3];
        jackpotFilterParams.siteCode = entitySettings.gameProviderSiteCodes?.[gameProviderCode];
        return [
            gameProviderCode, jackpotId,
            JackpotFilterBuilder.build(filterTemplate, jackpotFilterParams)
        ].join(";");
    }
    return jackpotId;
}

export async function getJackpotAuthToken(): Promise<string> {
    return TokenUtils.generateAccessToken({
        userId: -1,
        entityId: -1,
        username: "anonymous",
        grantedPermissions: [
            "jackpot:instance:view",
            "jackpot:type:view",
            "jackpot:instance:create",
            "jackpot:instance:edit"
        ]
    });
}

export async function getJackpotAuditAuthToken(): Promise<string> {
    return TokenUtils.generateAccessToken({
        userId: -1,
        entityId: -1,
        username: "anonymous",
        grantedPermissions: ["jackpot"],
    });
}
