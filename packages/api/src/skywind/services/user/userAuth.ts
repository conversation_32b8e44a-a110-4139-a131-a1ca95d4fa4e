import { UserModel } from "../../models/user";
import {
    AccessTokenInfo,
    AuthInfo,
    LoginInfo,
    MAP_FORCE_PASSWORD_CHANGE_PERIOD_TYPE,
    User,
    UserInfo,
    UserType
} from "../../entities/user";
import * as Errors from "../../errors";
import { BaseEntity } from "../../entities/entity";
import {
    blockUser,
    CaptchaInfo,
    Captcharized,
    checkUserBlocked,
    checkUserBlockedByLoginAttempts,
    createAuthCodeSentMarker,
    createGASecret,
    createGATotpUri,
    createResetToken,
    createTwoFACode,
    encryptPassword,
    generateAccessToken,
    incrementFailedLoginsCountAndBlockUserIfRequired,
    SECURITY_AUTH_TYPE,
    unblockUser,
    verifyAuthCodeSentMarker,
    verifyGACode,
    verifyGASelection,
    verifyTwoFACode
} from "../security";
import { sendAuthCodeEmail } from "../../utils/emails";
import { EmailTemplate, EntitySettings } from "../../entities/settings";
import { default as getUserService, getUser, UserImpl } from "./user";
import { getEntitySettings, USER_WHITELIST_KEY } from "../settings";
import { DEFAULT_PASSWORD_PATTERN, TIME_CONSTS, TWO_FA_TYPE } from "../../utils/common";
import { TwoFATokenData } from "../../utils/token";
import { sendAuthCodeSms } from "../../utils/sms";
import config from "../../config";
import { getAuthSessionService } from "../authSessionService";
import logger from "../../utils/logger";
import { getIpLocationService } from "../../utils/iplocation";

export const userDefaultPasswordValidator = new RegExp(DEFAULT_PASSWORD_PATTERN);

export interface ChangePasswordData {
    password: string;
    newPassword: string;
}

export interface UserAuthData {
    username: string;
    password: string;
    language?: string;
    referer?: string;
    ip?: string;
}

export interface WithSecretKey {
    secretKey: string;
}

export interface WithResolvedIp {
    resolvedIp: string;
}

export interface PasswordResetRequest extends WithSecretKey, Captcharized {
    identifier: string;
    resolvedIp?: string;
    domain: string;
}

export interface PasswordResetResponse {
    extraData?: CaptchaInfo;
}

export interface PasswordResetData extends WithSecretKey {
    username: string;
    token: string;
    newPassword: string;
}

export interface SecondStepLoginInfo {
    token: string;
    authCode: string;
    authType;
}

export interface SecondStepAuthSelectInfo {
    authType: string;
    token: string;
    contactInfo?: string;
}

export interface UserAuthTypesInfo {
    defaultAuthType?: string;
    userAuthTypes: string[];
}

export interface SecondStepAuthChallengeInfo extends UserAuthTypesInfo {
    token: string;
    contactInfo: {
        [contactType: string]: string
    };
}

export interface AddSecondStepAuthInfo extends SecondStepAuthSelectInfo {
    authCode: string;
    setAsDefault?: boolean;
}

export interface AuthCodeSentResponse {
    authType: string;
    contactInfo: {
        [contactType: string]: string
    };
}

export interface TotpAuthCodeSentResponse extends AuthCodeSentResponse {
    totpUri: string;
    gaSecretKey: string;
}

export interface UserAuthService {
    login(data: UserAuthData,
          langForAuthCodeMessage?: string,
          referer?: string): Promise<LoginInfo>;

    makeChallengeForSecondStepAuth(userInfo: TwoFATokenData,
                                   authType: string,
                                   contactInfo: string,
                                   langForAuthCodeMessage?: string): Promise<AuthCodeSentResponse>;

    makeQRCodeForGoogleAuthSelection(userInfo: TwoFATokenData): Promise<TotpAuthCodeSentResponse>;

    setSecondStepAuthType(userInfo: TwoFATokenData,
                          authCode: string,
                          authType: string,
                          contactInfo?: string): Promise<LoginInfo>;

    addSecondStepAuthType(userInfo: TwoFATokenData, authCode: string,
                          authType: string, contactInfo?: string, setAsDefault?: boolean): Promise<void>;

    secondStepLogin(userInfo: TwoFATokenData, authCode: string, authType: string): Promise<LoginInfo>;

    loginRefresh(username: string): Promise<LoginInfo>;

    unlockLogin(username: string): Promise<void>;

    resetUserAuth(username: string, twoFaTypeToRemove: string): Promise<UserInfo>;

    setUserAuthAsDefault(username: string, defaultTwoFaType: string): Promise<UserInfo>;

    getUserAuthTypes(username: string): Promise<UserAuthTypesInfo>;
}

export function getUserAuthService(entity: BaseEntity): UserAuthService {
    return new UserAuthServiceImpl(entity);
}

export class UserAuthServiceImpl implements UserAuthService {
    protected log = logger();

    constructor(private entity: BaseEntity) {
    }

    public async login({ password, username, language = "*", referer, ip }: UserAuthData): Promise<LoginInfo> {

        let userBlockedByLoginAttempts: boolean = false;
        const masterUser: boolean = this.entity.isMaster();
        if (!masterUser) {
            userBlockedByLoginAttempts = await checkUserBlockedByLoginAttempts(this.entity.key, username);
        }
        let user: UserModel;
        try {
            await this.validateEntity(referer, ip);
            user = await this.getUser(this.entity.id, username);
        } catch (e) {
            this.log.error(e);
            return Promise.reject(new Errors.GenericUserLoginError());
        }
        if (!masterUser) {
            checkUserBlocked(user);
        }
        if (userBlockedByLoginAttempts) {
            await blockUser(user);
            return Promise.reject(new Errors.UserAuthenticationBlocked(SECURITY_AUTH_TYPE.USER));
        }

        const passwordIsValid: boolean = await this.checkUserPassword(user, password);
        if (!passwordIsValid) {
            await incrementFailedLoginsCountAndBlockUserIfRequired(this.entity.key, user);
            this.log.error("Password does not match");
            return Promise.reject(new Errors.GenericUserLoginError());
        }

        const settings: EntitySettings = await getEntitySettings(this.entity.path);

        if (config.twoFA.isEnabled && settings?.twoFactorAuthSettings?.isAuthEnabled) {
            const twoFALoginData = await this.checkAndGetTwoFALoginData(user, settings, language);
            return twoFALoginData as any;
        }

        await this.checkPasswordResetRequired(user, this.entity, settings);

        // Logging Last user success log in
        // With silent: true the updatedAt timestamp will not be updated.
        // Note: Silent work only in case: update(keys: Object, options?: InstanceUpdateOptions)
        // without "await"
        user.update({ lastLogin: new Date() }, { silent: true });

        return this.createLoginInfo(this.entity, new UserImpl(user), settings);
    }

    public async validateEntity(referer: string | undefined, ip: string | undefined) {
        if (this.entity.isSuspended()) {
            throw new Errors.ParentSuspendedError();
        }
        if (config.checkEntityDomain) {
            if (!this.entity.domainExists(referer)) {
                throw new Errors.DomainForbiddenError(referer);
            }
        }
        if (config.getServerName() === "BO" && ip) {
            const settings = await getEntitySettings(this.entity.path);
            if (settings) {
                const list = settings[USER_WHITELIST_KEY] || [];
                if (list.length && getIpLocationService().isWhitelisted(ip, list) === false) {
                    throw new Errors.OperationForbidden(`IP:${ip} is restricted`);
                }
            }
        }
    }

    public async checkAndGetTwoFALoginData(user: UserModel,
                                           settings: EntitySettings,
                                           langForAuthCodeMessage: string): Promise<SecondStepAuthChallengeInfo> {
        const userImpl = new UserImpl(user);
        const authToken = await userImpl.generateTFAToken();

        await this.cleanUserAuthMethodsThatWereRemovedFromEntity(userImpl, settings);

        if (!userImpl.tfaIsConfigured()) {
            return Promise.reject(
                new Errors.TwoFATypeNotSetError(
                    settings.twoFactorAuthSettings.authOptions,
                    authToken,
                    userImpl.hasPhone()));
        } else {
            const secondStepAuthInfo: SecondStepAuthChallengeInfo = {
                userAuthTypes: userImpl.authInfo.authTypes,
                contactInfo: {},
                token: authToken
            };
            if (userImpl.email) {
                secondStepAuthInfo.contactInfo.email = this.starEmail(userImpl.email)
            }
            if (userImpl.phone) { // phone may be absent in user's profile
                secondStepAuthInfo.contactInfo.sms = this.starPhone(userImpl.phone);
            }
            const defaultEnabledType = userImpl.defaultTwoFAType &&
                (settings.twoFactorAuthSettings.authOptions || []).includes(userImpl.defaultTwoFAType);

            if (defaultEnabledType) {
                await this.generateSecondStepAuthChallenge(settings, userImpl, userImpl.defaultTwoFAType,
                    langForAuthCodeMessage, true);
                secondStepAuthInfo.defaultAuthType = userImpl.defaultTwoFAType;
                return secondStepAuthInfo;
            } else {
                return secondStepAuthInfo;
            }
        }
    }

    public async checkPasswordResetRequired(user: UserModel,
                                            entity: BaseEntity,
                                            settings: EntitySettings): Promise<void> {
        if (config.userPasswordChangeCheck.isEnabled) {
            if (settings && settings.isPasswordChangeEnabled) {
                const passwordChangedAt = user.get("passwordChangedAt");
                if (!passwordChangedAt) {
                    const token = await createResetToken(entity.key, user.get("username"), "resetPasswordTokens");
                    return Promise.reject(new Errors.ChangePasswordError(token));
                }
            }
        }

        if (config.userPasswordForceChangeCheck.isEnabled) {
            if (settings && settings.isPasswordForceChangeEnabled) {
                const passwordChangedAt: any = user.get("passwordChangedAt") || user.get("createdAt");

                const forcePeriod = this.getForcePeriod(user, settings);

                const forceChangeIsNeeded = passwordChangedAt
                    && user.get("userType") === UserType.BO
                    && Math.abs(new Date().getTime() - passwordChangedAt) > forcePeriod;

                if (forceChangeIsNeeded) {
                    const token = await createResetToken(entity.key, user.get("username"), "resetPasswordTokens");
                    return Promise.reject(new Errors.ChangePasswordError(token));
                }
            }
        }
    }

    /**
     * Gets force period in miliseconds. Firstly checks user.userAuth for force period data, then checks
     * entity settings, and if not present fallbacks to config.
     * Usually, 3 months is default value (from config)
     */
    private getForcePeriod(user: UserModel, settings: EntitySettings): number {
        const authInfo: AuthInfo = user.get("authInfo");

        if (authInfo && authInfo.forcePasswordChangePeriod) {
            return authInfo.forcePasswordChangePeriod *
                MAP_FORCE_PASSWORD_CHANGE_PERIOD_TYPE.get(authInfo.forcePasswordChangePeriodType);
        }
        const daysPeriod = settings.passwordForceChangePeriod || config.userPasswordForceChangeCheck.daysPeriod;
        return daysPeriod * TIME_CONSTS.DAY_TICKS;
    }

    private async checkUserPassword(user: UserModel, passwordToCheck: string): Promise<boolean> {
        const salt: string = user.get("salt");
        const password: string = await encryptPassword(salt, passwordToCheck);
        const userPassword = user.get("password");
        return password === userPassword;
    }

    /**
     * Handles case when somebody removed user's authType from entity settings.
     * Cleans all user's authTypes that are not present within his entity settings
     */
    private async cleanUserAuthMethodsThatWereRemovedFromEntity(userImpl: UserImpl,
                                                                settings: EntitySettings): Promise<any> {
        if (!userImpl.tfaIsConfigured()) {
            return;
        }

        let hasAtLeastOneChange = false;
        for (const userAuthType of userImpl.authInfo.authTypes) {
            if (!settings.twoFactorAuthSettings.authOptions.includes(userAuthType)) {
                hasAtLeastOneChange = true;
                const index = userImpl.authInfo.authTypes.indexOf(userAuthType);
                if (index > -1) {
                    userImpl.authInfo.authTypes.splice(index, 1);
                    if (userImpl.authInfo.defaultAuthType === userAuthType) {
                        delete userImpl.authInfo["defaultAuthType"];
                    }
                }
            }
        }

        if (hasAtLeastOneChange) {
            return userImpl.save();
        }
    }

    private async createLoginInfo(entity: BaseEntity, user: User, settings: EntitySettings): Promise<LoginInfo> {
        const sessionId = await getAuthSessionService()
            .createSession(user.id, user.userType, settings?.denyBoSimultaneousLogin);
        const isSuperAdmin = entity.isMaster() && user.hasSuperAdminRole();

        const tokenInfo: AccessTokenInfo = {
            username: user.username,
            key: entity.key,
            entityId: entity.id,
            userId: user.id,
            userType: user.userType,
            sessionId: sessionId,
            isSuperAdmin
        };

        // generate access token
        const accessToken: string = await generateAccessToken(tokenInfo, user);
        const lastHistoryInfo = user.passwordHistory && user.passwordHistory[0];
        const lastPasswordUpdate = lastHistoryInfo?.createdAt;
        return {
            accessToken,
            key: tokenInfo.key,
            username: tokenInfo.username,
            lastPasswordUpdate,
            grantedPermissions: {
                permissions: user.grantedPermissions
            }
        };
    }

    private async generateSecondStepAuthChallenge(settings: EntitySettings,
                                                  user: UserImpl,
                                                  authType: SECURITY_AUTH_TYPE,
                                                  lang: string,
                                                  isLogin: boolean = false): Promise<void> {
        const authCode: string = await createTwoFACode(this.entity.key, user.username);
        switch (authType) {
            case TWO_FA_TYPE.SMS:
                if (isLogin) {
                    await verifyAuthCodeSentMarker(this.entity.key, user.username, authType);
                    await this.sendAuthCodeViaSms(settings, authCode, user, lang);
                    await createAuthCodeSentMarker(this.entity.key, user.username, authType);
                } else {
                    await this.sendAuthCodeViaSms(settings, authCode, user, lang);
                }
                break;
            case TWO_FA_TYPE.EMAIL:
                if (isLogin) {
                    await verifyAuthCodeSentMarker(this.entity.key, user.username, authType);
                    await this.sendAuthCodeViaEmail(settings, authCode, user, lang);
                    await createAuthCodeSentMarker(this.entity.key, user.username, authType);
                } else {
                    await this.sendAuthCodeViaEmail(settings, authCode, user, lang);
                }
                break;
            case TWO_FA_TYPE.GOOGLE:
                // no need to do anything - totp password is generated automatically on user's device
                break;
            default:
                break;
        }
    }

    /**
     * Sends second step auth code via sms
     */
    private async sendAuthCodeViaSms(settings: EntitySettings,
                                     authCode: string,
                                     user: UserImpl,
                                     lang: string): Promise<void> {
        let smsTemplate: string;
        // config.twilio.authSmsTemplate is used if no sms template specified at all in entity settings
        if (settings.twoFactorAuthSettings.smsTemplates && lang) {
            if (lang === "*") {
                smsTemplate = settings.twoFactorAuthSettings.smsTemplates.default || config.twilio.authSmsTemplate;
            } else {
                smsTemplate = settings.twoFactorAuthSettings.smsTemplates[lang]
                    || settings.twoFactorAuthSettings.smsTemplates.default
                    || config.twilio.authSmsTemplate;
            }
        } else {
            smsTemplate = config.twilio.authSmsTemplate;
        }

        await sendAuthCodeSms(user.phone, authCode, user.username, smsTemplate);
    }

    /**
     * Sends second step auth code via email
     */
    private async sendAuthCodeViaEmail(settings: EntitySettings,
                                       authCode: string,
                                       user: UserImpl,
                                       lang: string): Promise<void> {
        let mailTemplate: EmailTemplate;
        if (settings.twoFactorAuthSettings.mailTemplates && lang) {
            if (lang === "*") {
                mailTemplate = settings.twoFactorAuthSettings.mailTemplates.default;
            } else {
                mailTemplate = settings.twoFactorAuthSettings.mailTemplates[lang]
                    || settings.twoFactorAuthSettings.mailTemplates.default;
            }
        } else {
            mailTemplate = settings.twoFactorAuthSettings.mailTemplates.default;
        }
        await sendAuthCodeEmail(user.email, user.username, authCode, mailTemplate);
    }

    /**
     * Generates challenge for user. Used for sms and email types.
     */
    public async makeChallengeForSecondStepAuth(userInfo: TwoFATokenData,
                                                authType: SECURITY_AUTH_TYPE,
                                                contactInfo?: string,
                                                langForAuthCodeMessage: string = "*"):
        Promise<AuthCodeSentResponse> {

        const user = await this.getUser(this.entity.id, userInfo.username);
        const settings: EntitySettings = await getEntitySettings(this.entity.path);
        await this.checkAuthTypeIsAllowed(authType, settings);

        const userImpl = new UserImpl(user);
        userImpl.phone = userImpl.phone || contactInfo;

        if (TWO_FA_TYPE.SMS === authType && !userImpl.phone) {
            return Promise.reject(new Errors.ValidationError("Phone number is required for sms auth"));
        }

        await this.generateSecondStepAuthChallenge(settings, userImpl, authType, langForAuthCodeMessage);
        return {
            authType: authType,
            contactInfo: TWO_FA_TYPE.SMS === authType ?
                { sms: this.starPhone(userImpl.phone) } :
                { email: this.starEmail(userImpl.email) }
        };
    }

    /**
     * Returns rejected promise if auth type is not present within entity settings
     */
    private async checkAuthTypeIsAllowed(authType: string,
                                         settings: EntitySettings): Promise<void> {
        if (!settings.twoFactorAuthSettings
            || !settings.twoFactorAuthSettings.isAuthEnabled
            || !settings.twoFactorAuthSettings.authOptions) {
            return Promise.reject(new Errors.TwoFANotConfiguredForEntity());
        }
        if (settings.twoFactorAuthSettings.authOptions.indexOf(authType) === -1) {
            return Promise.reject(new Errors.AuthTypeIsNotAllowed(authType));
        }
    }

    /**
     * Generates qr code uri for google authenticator prior to setting google as second step auth type.
     */
    public async makeQRCodeForGoogleAuthSelection(userInfo: TwoFATokenData): Promise<TotpAuthCodeSentResponse> {

        const user = await this.getUser(this.entity.id, userInfo.username);
        await this.checkAuthTypeIsAllowed(TWO_FA_TYPE.GOOGLE, await getEntitySettings(this.entity.path));

        const userImpl = new UserImpl(user);
        const gaSecretKey = await createGASecret(this.entity.key, userImpl.username);
        const totpUri = await createGATotpUri(gaSecretKey, userImpl.email);
        return {
            authType: TWO_FA_TYPE.GOOGLE,
            contactInfo: { email: this.starEmail(userImpl.email) },
            totpUri,
            gaSecretKey
        };
    }

    /**
     * Replace part of an email with * char
     */
    private starEmail(email: string): string {
        const domain = email.substring(email.indexOf("@"));
        const withoutDomain = email.substring(0, email.indexOf("@"));
        if (withoutDomain.length <= 3) {
            let res = "";
            withoutDomain.split("").forEach(() => res += "*");
            return res + domain;
        }
        let result = withoutDomain.substring(0, 3);
        for (let i = 0; i < (withoutDomain.length - 3); i++) {
            result += "*";
        }
        return result + domain;
    }

    /**
     * Replace part of a phone with * char
     */
    private starPhone(phone: string): string {
        return phone.replace(/\d(?=\d{4})/g, "*");
    }

    /**
     * Verifies auth code and stores selected authentication type and info (phone for sms, secret key for google auth)
     */

    public async setSecondStepAuthType(userInfo: TwoFATokenData,
                                       authCode: string,
                                       authType: string,
                                       contactInfo?: string
    ): Promise<LoginInfo> {
        const user = await this.getUser(this.entity.id, userInfo.username);
        const instance = new UserImpl(user);
        const entitySettings = await getEntitySettings(this.entity.path);

        await this.checkAuthTypeIsAllowed(authType, entitySettings);

        const authInfo: any = instance.authInfo || {};

        if (instance.tfaIsConfigured()) {
            return Promise.reject(new Errors.ValidationError("User already has at least one auth type"));
        }

        const phone = instance.phone || contactInfo;
        if (TWO_FA_TYPE.SMS === authType && !phone) {
            return Promise.reject(new Errors.ValidationError("Phone number is required for sms auth"));
        }

        if (TWO_FA_TYPE.GOOGLE === authType) {
            authInfo.gaSecret = await verifyGASelection(this.entity.key, userInfo.username, authCode);
        } else {
            await verifyTwoFACode(this.entity.key, userInfo.username, authCode);
        }

        if (!authInfo.authTypes) {
            authInfo.authTypes = [];
        }
        authInfo.authTypes.push(authType);
        authInfo.defaultAuthType = authType;

        user.changed("authInfo", true);
        await user.update({ phone, authInfo: authInfo });

        await this.checkPasswordChanged(this.entity, user);

        // Logging Last user success log in
        // With silent: true the updatedAt timestamp will not be updated.
        await user.update({ lastLogin: new Date() }, { silent: true });

        return this.createLoginInfo(this.entity, new UserImpl(user), entitySettings);
    }

    /**
     * Verifies auth code and adds selected authentication type and info (phone for sms, secret key for google auth)
     */
    public async addSecondStepAuthType(userInfo: TwoFATokenData,
                                       authCode: string,
                                       authType: string,
                                       contactInfo?: string,
                                       setAsDefault?: boolean): Promise<void> {

        const user = await this.getUser(this.entity.id, userInfo.username);
        const authInfo: any = user.get("authInfo") || {};

        if (authInfo.authTypes && authInfo.authTypes.includes(authType)) {
            return;
        }

        await this.checkAuthTypeIsAllowed(authType, await getEntitySettings(this.entity.path));

        const phone = user.get("phone") || contactInfo;
        if (TWO_FA_TYPE.SMS === authType && !phone) {
            return Promise.reject(new Errors.ValidationError("Phone number is required for sms auth"));
        }

        let gaSecret;
        if (TWO_FA_TYPE.GOOGLE === authType) {
            gaSecret = await verifyGASelection(this.entity.key, userInfo.username, authCode);
        } else {
            await verifyTwoFACode(this.entity.key, userInfo.username, authCode);
        }

        if (TWO_FA_TYPE.GOOGLE === authType) {
            authInfo.gaSecret = gaSecret;
        }

        if (!authInfo.authTypes) {
            authInfo.authTypes = [];
        }
        authInfo.authTypes.push(authType);
        if (setAsDefault) {
            authInfo.defaultAuthType = authType;
        }

        user.changed("authInfo", true);
        await user.update({ phone, authInfo: authInfo });
    }

    public async secondStepLogin(userInfo: TwoFATokenData,
                                 authCode: string,
                                 authType: string
    ): Promise<LoginInfo> {
        const user = await this.getUser(this.entity.id, userInfo.username);
        const entitySettings = await getEntitySettings(this.entity.path);
        await this.checkAuthTypeIsAllowed(authType, entitySettings);

        if (TWO_FA_TYPE.GOOGLE === authType) {
            await verifyGACode(user.get("authInfo").gaSecret, authCode);
        } else {
            await verifyTwoFACode(this.entity.key, userInfo.username, authCode);
        }

        await this.checkPasswordChanged(this.entity, user);

        // Logging Last user success log in
        // With silent: true the updatedAt timestamp will not be updated.
        await user.update({ lastLogin: new Date() }, { silent: true });

        return this.createLoginInfo(this.entity, new UserImpl(user), entitySettings);
    }

    private async getUser(entityId: number, username: string): Promise<UserModel> {
        return getUser(entityId, username);
    }

    private async checkPasswordChanged(entity: BaseEntity, user: UserModel): Promise<void> {
        if (config.userPasswordChangeCheck.isEnabled) {
            const settings = await getEntitySettings(entity.path);
            if (settings && settings.isPasswordChangeEnabled) {
                const passwordChangedAt = user.get("passwordChangedAt");
                if (!passwordChangedAt) {
                    const token = await createResetToken(entity.key, user.get("username"), "resetPasswordTokens");
                    return Promise.reject(new Errors.ChangePasswordError(token));
                }
            }
        }
    }

    public async loginRefresh(username: string): Promise<LoginInfo> {
        if (this.entity.isSuspended()) {
            return Promise.reject(new Errors.ParentSuspendedError());
        }

        const service = getUserService(this.entity);
        const user: User = await service.findOne(username);
        if (user.isSuspended()) {
            return Promise.reject(new Errors.PlayerIsSuspended());
        }
        const entitySettings = await getEntitySettings(this.entity.path);
        return this.createLoginInfo(this.entity, user, entitySettings);
    }

    public async unlockLogin(username: string): Promise<void> {
        if (this.entity.isSuspended()) {
            return Promise.reject(new Errors.ParentSuspendedError());
        }
        const user = await this.getUser(this.entity.id, username);
        return unblockUser(this.entity.key, user);
    }

    public async resetUserAuth(username: string, twoFaTypeToRemove: string): Promise<UserInfo> {
        const service = getUserService(this.entity);

        const user = await service.findOne(username);

        if (!user.authInfo || !user.authInfo.authTypes.includes(twoFaTypeToRemove)) {
            return Promise.reject(new Errors.TwoFATypeNotSetError(undefined, undefined, undefined));
        }

        const index = user.authInfo.authTypes.indexOf(twoFaTypeToRemove);
        if (index > -1) {
            user.authInfo.authTypes.splice(index, 1);
            if (user.authInfo.defaultAuthType === twoFaTypeToRemove) {
                delete user.authInfo["defaultAuthType"];
            }
            if (twoFaTypeToRemove === TWO_FA_TYPE.GOOGLE) {
                delete user.authInfo["gaSecret"];
            }
        }

        return user.save();
    }

    public async setUserAuthAsDefault(username: string, defaultTwoFaType: SECURITY_AUTH_TYPE): Promise<UserInfo> {
        const service = getUserService(this.entity);

        const user = await service.findOne(username);

        if (!user.authInfo || !user.authInfo.authTypes.includes(defaultTwoFaType)) {
            return Promise.reject(new Errors.TwoFATypeNotSetError(undefined, undefined, undefined));
        }

        user.authInfo.defaultAuthType = defaultTwoFaType;

        return user.save();
    }

    public async getUserAuthTypes(username: string): Promise<UserAuthTypesInfo> {
        const service = getUserService(this.entity);

        const user = await service.findOne(username);

        if (user.authInfo) {
            return { userAuthTypes: user.authInfo.authTypes, defaultAuthType: user.authInfo.defaultAuthType };
        }

        return undefined;
    }
}
