import { TerminalDBInstance } from "../models/terminal";
import { Models } from "../models/models";
import { getBrandPlayerService, PlayerImpl } from "./brandPlayer";
import * as Errors from "../errors";
import * as Sequelize from "sequelize";
import * as FilterService from "./filter";
import { PagingHelper } from "../utils/paginghelper";
import { generateTerminalToken, TerminalTokenData } from "../utils/token";
import { BaseEntity } from "../entities/entity";
import config from "../config";
import { createPlayerSessionFacade } from "./player/playerSessionFacade";
import { EntitySettings } from "../entities/settings";
import { getEntitySettings } from "./settings";
import { literal, Op, WhereOptions } from "sequelize";

const TerminalModel = Models.TerminalModel;
const PlayerModel = Models.PlayerModel;

export interface CreateData {
    lobbyId?: number;
    brandId?: number;
    status?: string;
    title?: string;
}

export interface LogoutData {
    code: string;
}

const DEFAULT_SORT_KEY = "createdAt";

export const sortableKeys = [
    "status",
    "title",
    "lobbyId",
    "createdAt",
    "updatedAt",
];

export const queryParamsKeys = [
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
    "status",
    "id",
    "title",
    "lobbyId",
    "createdAt",
    "updatedAt",
];

export class TerminalImpl {
    public id: number;
    public title: string;
    public brandId: number;
    public lobbyId: number;
    public status: string;
    public createdAt: Date;
    public updatedAt: Date;
    public player: any;

    constructor(item?: TerminalDBInstance) {
        if (!item) {
            return;
        }
        this.id = item.get("id");
        this.title = item.get("title");
        this.brandId = item.get("brandId");
        this.lobbyId = item.get("lobbyId");
        this.status = item.get("status");
        this.createdAt = item.get("createdAt");
        this.updatedAt = item.get("updatedAt");
        this.player = item.get("player");
        if (this.player) {
            this.player = new PlayerImpl(this.player);
        }
    }

    public async toInfo() {
        return {
            id: this.id,
            title: this.title,
            brandId: this.brandId,
            lobbyId: this.lobbyId,
            status: this.status,
            player: this.player ? await this.player.toInfoWithBalances() : null,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}

export async function create(brandId: number, data: CreateData): Promise<TerminalImpl> {
    const checkCreatedItem: TerminalDBInstance = await TerminalModel.findOne({
        where: { brandId: brandId, title: data.title },
    });
    if (checkCreatedItem) {
        return Promise.reject(new Errors.TerminalAlreadyExists());
    }
    const record: TerminalImpl = new TerminalImpl();
    record.title = data.title;
    record.brandId = brandId;
    record.lobbyId = data.lobbyId;
    record.status = data.status || "normal";

    try {
        const item: TerminalDBInstance = await TerminalModel.create(record);
        return new TerminalImpl(item);
    } catch (error) {
        if (error instanceof Sequelize.UniqueConstraintError) {
            return Promise.reject(new Errors.TerminalAlreadyExists());
        }
        if (error instanceof Sequelize.ForeignKeyConstraintError) {
            return Promise.reject(new Errors.LobbyNotFound());
        }
        return Promise.reject(error);
    }
}

export async function update(brandId: number, terminalId: number, data: CreateData): Promise<TerminalImpl> {
    const terminal: TerminalImpl = await getTerminalById(brandId, terminalId);
    const updateData: CreateData = {};
    if (typeof data.title !== "undefined") {
        updateData.title = data.title;
    }
    if (typeof data.lobbyId !== "undefined") {
        updateData.lobbyId = data.lobbyId;
    }
    if (typeof data.status !== "undefined") {
        updateData.status = data.status;
    }
    Object.assign(terminal, updateData);

    try {
        await TerminalModel.update(updateData, { where: { brandId: brandId, id: terminalId } });
    } catch (err) {
        return Promise.reject(new Errors.TerminalUpdateFail());
    }
    return terminal;
}

export async function logoutPlayer(brandId: number, data: LogoutData): Promise<void> {
    const player = await getBrandPlayerService().getPlayer(brandId, data.code);
    await createPlayerSessionFacade().kill({
        brandId: player.brandId,
        playerCode: player.code,
        reason: "Logout Player"
    });
}

const balanceMapping = (terminal) => {
    const terminalImpl = new TerminalImpl(terminal);
    return terminalImpl.toInfo();
};

const appendSearchFilter = (query: WhereOptions<any>, searchString: string) => {
    const iLike = { [Op.iLike]: `%${searchString}%` };
    query[Op.or as any] = {
        title: iLike,
        "$player.code$": iLike,
    };
};

export async function getAllTerminals(request: any,
                                      brandId: number,
                                      query?: WhereOptions<any>): Promise<any[]> {
    try {
        const sortBy = FilterService.getSortKey(query, sortableKeys, DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "DESC";
        query["brandId"] = brandId;

        if (request.search) {
            appendSearchFilter(query, request.search);
        }
        return PagingHelper.findAsyncAndCountAll(TerminalModel, {
            where: query,
            offset: FilterService.valueFromQuery(query, "offset"),
            limit: FilterService.valueFromQuery(query, "limit"),
            order: literal(`"${sortBy}" ${sortOrder}`),
            include: [{ model: PlayerModel, as: "player" }],
        }, terminal => balanceMapping(terminal));
    } catch (error) {
        return Promise.reject(new Errors.TerminalQueryFail());
    }
}

export async function getTerminal(brandId: number, terminalId: number): Promise<TerminalImpl> {
    return getTerminalById(brandId, terminalId);
}

export async function deleteTerminal(brandId: number, terminalId: number): Promise<TerminalImpl> {
    const item: TerminalDBInstance = await TerminalModel.findOne({
        where: { brandId: brandId, id: terminalId },
    });
    if (item) {
        await item.destroy();
    } else {
        return Promise.reject(new Errors.TerminalNotFound());
    }
}

async function getTerminalById(brandId: number, terminalId: number): Promise<TerminalImpl> {
    const item: TerminalDBInstance = await TerminalModel.findOne({
        where: { brandId: brandId, id: terminalId },
    });
    if (item) {
        return new TerminalImpl(item);
    } else {
        return Promise.reject(new Errors.TerminalNotFound());
    }
}

export async function getTerminalToken(brand: BaseEntity & { defaultCurrency?: string, defaultLanguage?: string },
                                       data: TerminalTokenData): Promise<string> {
    data.brandId = brand.id;
    data.operatorType = brand.type;

    data.playerUrl = data.playerUrl || config.terminalSettings.playerUrl;
    data.terminalUrl = data.terminalUrl || config.terminalSettings.terminalUrl;
    data.lobbyUrl = data.lobbyUrl || config.terminalSettings.lobbyUrl;

    if (!brand.isMaster()) {
        data.defaultCurrency = brand.defaultCurrency;
        data.defaultLanguage = brand.defaultLanguage;
    }

    const settings: EntitySettings = await getEntitySettings(brand.path);
    if (settings.env) {
        data.env = settings.env;
    }

    return generateTerminalToken(data);
}
