import * as Errors from "../../errors";
import {
    createPlayerGameSession,
    createPlayerLobbySession,
    LobbySessionInfo,
    PlayerGameSession,
    PlayerLobbySession
} from "@skywind-group/sw-management-playersession";
import { getRedisPool } from "../../storage/redis";
import config from "../../config";
import { AuditPlayerLobbySession } from "./auditPlayerSession";

export interface PlayerSessionParam {
    brandId: number;
    playerCode: string;
}

export interface PlayerSessionKillParam extends PlayerSessionParam {
    reason?: string;
}

export interface PlayerSessionFindParam extends PlayerSessionParam {
    sessionId?: string;
}

export interface PlayerSessionCreateParam extends PlayerSessionParam {
    sessionId: string;
    playerId: number;
}

export interface PlayerSessionFacade {
    create(args: PlayerSessionCreateParam): Promise<void>;

    kill(args: PlayerSessionKillParam): Promise<boolean>;

    find(args: PlayerSessionFindParam, enableSessionCheck: boolean): Promise<LobbySessionInfo>;

    sessionExists(args: PlayerSessionParam): Promise<boolean>;
}

export class PlayerSessionFacadeImpl implements PlayerSessionFacade {
    constructor(private readonly lobbySession: PlayerLobbySession,
                private readonly gameSession: PlayerGameSession<any>) {
    }

    public async create({ brandId, playerCode, playerId, sessionId }: PlayerSessionCreateParam): Promise<void> {
        await this.gameSession.kill(brandId, playerCode);
        await this.lobbySession.create(brandId, playerCode, { playerId, sessionId });
    }

    public async kill({ brandId, playerCode, reason }: PlayerSessionKillParam): Promise<boolean> {
        const gameResult = await this.gameSession.kill(brandId, playerCode);
        const lobbyResult = await this.lobbySession.kill(brandId, playerCode, reason);
        return gameResult || lobbyResult;
    }

    public async find(args: PlayerSessionFindParam, enableSessionCheck = true): Promise<LobbySessionInfo> {
        const existSession = await this.lobbySession.find(args.brandId, args.playerCode);
        if (!existSession) {
            return Promise.reject(new Errors.PlayerSessionNotFoundError());
        }
        if (enableSessionCheck && existSession.sessionId !== args.sessionId) {
            return Promise.reject(new Errors.PlayerSessionFinishedError());
        }
        return existSession;
    }

    public async sessionExists({ brandId, playerCode }: PlayerSessionParam): Promise<boolean> {
        return (await this.lobbySession.find(brandId, playerCode) !== null) ||
            (await this.gameSession.find(brandId, playerCode) > 0);
    }
}

export function createPlayerSessionFacade() {
    return new PlayerSessionFacadeImpl(
        new AuditPlayerLobbySession(createPlayerLobbySession(getRedisPool(), config.playerLoginToken.expiresIn)),
        createPlayerGameSession(getRedisPool(), config.startGameToken.expiresIn));
}
