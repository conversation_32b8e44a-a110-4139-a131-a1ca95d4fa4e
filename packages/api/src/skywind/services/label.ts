import { Op, Transaction, WhereOptions } from "sequelize";
import { lazy } from "@skywind-group/sw-utils";
import { Label, LabelGroup } from "../entities/label";
import { ILabelModel, LabelDBInstance } from "../models/label";
import * as Errors from "../errors";
import { getLabelGroupService, LabelGroupImpl } from "./labelGroup";
import { LABEL_GROUPS_RELATIONS_TYPES, LABEL_GROUPS_TYPES } from "../utils/common";
import { BaseEntity } from "../entities/entity";
import { CRUDServiceImpl, ID } from "./crudService";
import { sequelize as db } from "../storage/db";
import { getParentIds } from "./entity";
import { Models } from "../models/models";
import { encodeId } from "../utils/publicid";

const LabelModel = Models.LabelModel;
const EntityModel = Models.EntityModel;
const GameModel = Models.GameModel;
const LabelGroupModel = Models.LabelGroupModel;
const PromotionModel = Models.Promotion;
const EntityLabelModel = Models.EntityLabelModel;
const GameLabelModel = Models.GameLabelModel;
const PromotionLabelModel = Models.PromotionLabelModel;

export const queryParamsKeys = ["group", "type", "relationType"];
export const sortableKeys = ["title", "created_at", "updated_at"];
export const DEFAULT_SORT_KEY = "created_at";
export const SortMapping = {
    title: "title",
    created_at: "createdAt",
    updated_at: "updatedAt"
};

export const DEFAULT_SORT_ORDER = "DESC";

export class LabelImpl {
    private readonly id: number;
    private readonly title: string;
    private readonly groupId: number;
    private readonly group: LabelGroup;

    constructor(item?: LabelDBInstance) {
        if (!item) {
            return;
        }

        this.id = item.get("id");
        this.title = item.get("title");
        this.groupId = item.get("groupId");

        // TODO: need to define correct interface
        const labelGroupDB = item.get("labelgroup") || item.get("group");
        if (labelGroupDB) {
            this.group = new LabelGroupImpl(labelGroupDB as any).toInfo();
        }
    }

    public getId(): number {
        return this.id;
    }

    public toInfo(): Label {
        return {
            id: this.id,
            title: this.title,
            groupId: this.groupId,
            group: this.group,
        };
    }

    public toPublicInfo(): Label {
        return {
            id: encodeId(this.id),
            title: this.title,
            groupId: this.groupId,
            group: this.group,
        };
    }
}

export class LabelService extends CRUDServiceImpl<LabelDBInstance, Label, ILabelModel> {
    protected readonly labelGroupService = getLabelGroupService.get();

    public getModel(): ILabelModel {
        return LabelModel;
    }

    protected validateCreateData(data: Label): Label {
        if (!data.title || typeof data.title !== "string") {
            throw new Errors.ValidationError("Label title should be non-empty string");
        }

        if (!data.groupId) {
            throw new Errors.ValidationError("Label group id is required");
        }

        return {
            groupId: data.groupId,
            title: data.title
        };
    }

    protected async performCreate(cleanedData: Label,
                                  transaction: Transaction): Promise<LabelDBInstance> {
        const group = await this.labelGroupService.retrieve(cleanedData.groupId);

        const instances = await this.list({ where: { groupId: cleanedData.groupId, title: cleanedData.title } });

        if (instances.length) {
            throw new Errors.ValidationError(
                `Label already exists: ${cleanedData.title}, ${encodeId(cleanedData.groupId)}`);
        }

        const label = await super.performCreate(cleanedData, transaction);
        label.set("group", group.toJSON(), { raw: true });

        return label;
    }

    public async getGameLabels(gameCode: string): Promise<Label[]> {
        const results = await LabelModel.findAll({
            include: [
                {
                    model: GameModel,
                    where: {
                        code: gameCode
                    },
                    attributes: ["code"]
                }, {
                    model: LabelGroupModel,
                    as: "group"
                }
            ]
        });

        return results.map(item => {
            const { games, ...label } = item.toJSON() as any;
            return label;
        });
    }

    public async getEntityLabels(entity: BaseEntity): Promise<Label[]> {
        const allLabels = await LabelModel.findAll({
            include: [
                {
                    model: EntityModel,
                    as: "labelEntities",
                    where: {
                        id: {
                            [Op.in]: [...getParentIds(entity), entity.id]
                        }
                    },
                    attributes: ["id"]
                }, {
                    model: LabelGroupModel,
                    as: "group"
                }
            ]
        });

        const entityGroupIds = [];
        for (const group of allLabels) {
            const entityIds = group.get("labelEntities").map(item => item.get("id"));

            if (entityIds.includes(entity.id)) {
                entityGroupIds.push(group.get("groupId"));
            }
        }

        const results = [];
        for (const item of allLabels) {
            const entityIds = item.get("labelEntities").map(labelEntity => labelEntity.get("id"));

            if (entityGroupIds.includes(item.get("groupId")) && !entityIds.includes(entity.id)) {
                continue;
            }

            const { labelEntities, ...label } = item.toJSON();

            results.push(label);
        }

        return results;
    }

    public async getPromoLabels(entity: BaseEntity, promoId: number): Promise<Label[]> {
        const results = await LabelModel.findAll({
            include: [
                {
                    model: PromotionModel,
                    as: "labelPromotions",
                    where: {
                        id: promoId,
                        brandId: entity.id
                    },
                    attributes: ["brandId", "id"]
                }, {
                    model: LabelGroupModel,
                    as: "group"
                }
            ]
        });

        return results.map(item => {
            const { labelPromotions, ...label } = item.toJSON() as any;
            return label;
        });
    }

    public async setEntityLabels(entity: BaseEntity, labelIds: { id: number }[]): Promise<Label[]> {
        const labels = await this.validateLabels(labelIds, LABEL_GROUPS_TYPES.ENTITY);

        await db.transaction(async (transaction: Transaction) => {
            await EntityLabelModel.destroy({
                where: {
                    entityId: entity.id,
                },
                transaction
            });

            for (const label of labels) {
                await EntityLabelModel.create({
                    labelId: label.id,
                    entityId: entity.id,
                }, { transaction });
            }
        });

        return labels;
    }

    public async setGameLabels(gameCode: string, labelIds: { id: number }[]): Promise<Label[]> {
        const game = await GameModel.findOne({ where: { code: gameCode } });

        if (!game) {
            throw new Errors.ValidationError(`Game not found: ${gameCode}`);
        }

        const labels = await this.validateLabels(labelIds, LABEL_GROUPS_TYPES.GAME);

        await db.transaction(async (transaction: Transaction) => {
            await GameLabelModel.destroy({
                where: {
                    gameId: game.get("id"),
                },
                transaction: transaction
            });

            for (const label of labels) {
                await GameLabelModel.create({
                    labelId: label.id,
                    gameId: game.get("id"),
                }, { transaction });
            }
        });

        return labels;
    }

    public async setPromoLabels(entity: BaseEntity, promoId: number, labelIds: { id: number }[]): Promise<Label[]> {
        const promo = await PromotionModel.findOne({ where: { id: promoId, brandId: entity.id } });

        if (!promo) {
            throw new Errors.ValidationError(`Promo not found: ${encodeId(promoId)}`);
        }

        const labels = await this.validateLabels(labelIds, LABEL_GROUPS_TYPES.PROMOTION);

        await db.transaction(async (transaction: Transaction) => {
            await PromotionLabelModel.destroy({
                where: {
                    promoId: promo.get("id"),
                },
                transaction: transaction
            });

            for (const label of labels) {
                await PromotionLabelModel.create({
                    labelId: label.id,
                    promoId: promo.get("id"),
                }, { transaction });
            }
        });

        return labels;
    }

    public async destroy(id: ID): Promise<void> {
        return await db.transaction(async (transaction: Transaction) => {
            const label: LabelDBInstance = await this.getInstance(id, true,
                {
                    transaction,
                    include: [
                        {
                            model: LabelGroupModel,
                            as: "group"
                        }
                    ]
                });
            await this.validateRestrictions(new LabelImpl(label), transaction);
            return this.performDestroy(label, transaction);
        });
    }

    protected async validateLabels(labelIds: { id: number }[], type: LABEL_GROUPS_TYPES): Promise<Label[]> {
        if (!Array.isArray(labelIds)) {
            throw new Errors.ValidationError("Body should be an array of object with label id.");
        }
        for (const label of labelIds) {
            if (typeof label.id !== "number") {
                throw new Errors.ValidationError(`label.id is invalid: ${label.id}`);
            }
        }

        const ids = [...new Set(labelIds.map(item => item.id))];
        const items = await this.list({
            where: { id: { [Op.in]: ids } },
            include: [
                {
                    model: LabelGroupModel,
                    as: "group"
                }
            ]
        });

        const itemIds = items.map(item => item.get("id"));
        const notFoundLabels = ids.filter(id => !itemIds.includes(id));
        if (notFoundLabels.length) {
            throw new Errors.ValidationError(
                `Labels not found: ${notFoundLabels.map(id => encodeId(id))}`);
        }

        const labels = items.map(item => item.toJSON());

        const invalidLabels = labels.filter(label => label.group.type !== type);
        if (invalidLabels.length) {
            throw new Errors.ValidationError(
                `Cannot assign labels with not appropriated types: ${invalidLabels.map(label => label.title)}`);
        }

        for (const label of labels) {
            if (label.group.relationType !== LABEL_GROUPS_RELATIONS_TYPES.ONE_TO_ONE) {
                continue;
            }

            const labelsFromSameGroup = labels.filter(item => item.groupId === label.groupId && item.id !== label.id);

            if (labelsFromSameGroup.length) {
                throw new Errors.ValidationError(
                    `Only one label can be assigned from group: ${label.group.group}`);
            }
        }

        return labels;
    }

    private async validateRestrictions(label: LabelImpl,
                                       transaction: Transaction): Promise<void> {
        const group: LabelGroup = label.toInfo().group;
        if (group.type !== LABEL_GROUPS_TYPES.ENTITY) {
            throw new Errors.ValidationError("Wrong label type. Only entity labels can be deleted");
        }
        const relationsCount: number = await EntityLabelModel.count({
            where: {
                labelId: label.getId(),
            },
            transaction
        });
        if (relationsCount > 0) {
            throw new Errors.ValidationError("Label is used by some entities");
        }
    }
}

export const getLabelService = lazy(() => new LabelService());

export function getSearchableKeys() {
    return ["labelsId"];
}

export function prepareSearchQuery(searchQuery: WhereOptions<any>): WhereOptions<any> {
    if (searchQuery["labelsId"]) {
        searchQuery["id"] = searchQuery["labelsId"];
        delete searchQuery["labelsId"];
    }
    return searchQuery;
}
