import { measures, token } from "@skywind-group/sw-utils";
import { BaseEntity } from "../entities/entity";
import { AccessTokenData, PlayerLoginTokenData, TerminalTokenData } from "./token";

export class ContextVariables {
    private static readonly EntityId = "entity-id";
    private static readonly UserId = "user-id";
    private static readonly PlayerCode = "player-code";
    private static readonly SessionId = "session-id";
    private static readonly GameCode = "game-code";

    public static setUserAuth(accessToken: string) {
        if (accessToken) {
            const tokenData = token.parse<AccessTokenData>(accessToken);
            if (tokenData) {
                this.setEntityId(tokenData.entityId);
                this.setUserId(tokenData.userId);
                this.setSessionId(tokenData.sessionId);
            }
        }
    }

    public static setTerminalAuth(terminalToken: string) {
        if (terminalToken) {
            const tokenData = token.parse<TerminalTokenData>(terminalToken);
            if (tokenData) {
                this.setEntityId(tokenData.brandId);
            }
        }
    }

    public static setEntity(entity: BaseEntity) {
        this.setEntityId(entity.id);
    }

    public static setSessionId(sessionId: string) {
        measures.measureProvider.setContextVariable(ContextVariables.SessionId, sessionId, true);
    }

    public static setUserId(userId: number) {
        measures.measureProvider.setContextVariable(ContextVariables.UserId, userId, true);
    }

    public static setEntityId(entityId: number) {
        if (entityId) {
            measures.measureProvider.setContextVariable(ContextVariables.EntityId, entityId, true);
        }
    }

    public static setPlayerAuth(playerToken: string) {
        if (playerToken) {
            const tokenData = token.parse<PlayerLoginTokenData>(playerToken);
            this.setPlayerAuthData(tokenData);
        }
    }

    public static setPlayerAuthData(tokenData: PlayerLoginTokenData) {
        if (tokenData) {
            this.setEntityId(tokenData.brandId);
            measures.measureProvider.setContextVariable(ContextVariables.UserId, tokenData.userId, true);
            measures.measureProvider.setContextVariable(ContextVariables.PlayerCode, tokenData.playerCode, true);
            measures.measureProvider.setContextVariable(ContextVariables.SessionId, tokenData.sessionId, true);
            measures.measureProvider.setContextVariable(ContextVariables.GameCode, tokenData.gameCode, true);
        }
    }
}
