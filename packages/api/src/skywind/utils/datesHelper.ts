import * as Errors from "../errors";
import config from "../config";
import { PermissionsHolder } from "../services/security";
import { Request } from "express";

const ERROR_RANGE_MESSAGE = `You can see only history values for the past ${config.limits.periodLimit} months`;
export const unlimitedPermission: string = "report-without-limit";
export const datePostfixes: string[] = ["__gte", "__gt", "__lte", "__lt"];

interface DatesRange {
    [field: string]: string;

    ts__gte?: string;
    ts__gt?: string;
    ts__lte?: string;
    ts__lt?: string;
}

export function convertDatesToISO(request: Request, dateKeys: string[]): void {
    const data: DatesRange = request.query;
    if (!Object.keys(data).length) {
        return;
    }

    for (const dateKey of dateKeys) {
        if (data[dateKey] && !isNaN(Date.parse(data[dateKey]))) {
            data[dateKey] = new Date(data[dateKey]).toISOString();
        }

        for (const postfix of datePostfixes) {
            const key: string = dateKey + postfix;
            if (data[key] && !isNaN(Date.parse(data[key]))) {
                data[key] = new Date(data[key]).toISOString();
            }
        }
    }
}

export function defineDatesRangeLimits(
    { query, permissions }: Request & PermissionsHolder,
    dateKeys: string[],
    isEndDateRequired: boolean = false,
    startDateKeys: string[] = []
) {
    if (!Object.keys(query).length) {
        return;
    }
    if (permissions?.grantedPermissions.includes(unlimitedPermission)) {
        return;
    }

    const minStartDate = new Date();
    minStartDate.setMonth(minStartDate.getMonth() - config.limits.periodLimit);

    if (startDateKeys.length) {
        for (const key of startDateKeys) {
            const value = query[`${key}__gte`] || query[`${key}__gt`];
            if (value && new Date(value) < minStartDate) {
                throw new Errors.ValidationError(ERROR_RANGE_MESSAGE);
            }
        }
    }

    for (const key of dateKeys) {
        let startFrom = query[`${key}__gte`] || query[`${key}__gt`] || query.from;
        if (startFrom && new Date(startFrom) < minStartDate) {
            throw new Errors.ValidationError(ERROR_RANGE_MESSAGE);
        }
        if (!startFrom && startDateKeys.length === 0) {
            startFrom = query[`${key}__gte`] = minStartDate.toISOString();
        }

        const endTo = query[`${key}__lte`] || query[`${key}__lt`] || query.to;
        if (!endTo && isEndDateRequired) {
            throw new Errors.ValidationError(ERROR_RANGE_MESSAGE);
        }

        if (endTo && startFrom && new Date(endTo) < new Date(startFrom)) {
            throw new Errors.ValidationError("Time filter is incorrect. Start date should be before end date.");
        }
    }
}

export function setDatesRangeLimits({ query }: Request, name: string) {
    const minStartDate = new Date();
    minStartDate.setMonth(minStartDate.getMonth() - config.defaultNumberOfMonthsForAggrWinBetReports);

    if (!query[`${name}__gte`] && !query[`${name}__gt`]) {
        query[`${name}__gte`] = minStartDate.toISOString();
    }
    const startFrom = query[`${name}__gte`] || query[`${name}__gt`];
    if (startFrom) {
        const fromDate = new Date(startFrom);
        if (!query[`${name}__lte`] && !query[`${name}__lt`] && fromDate < minStartDate) {
            query[`${name}__gte`] = minStartDate.toISOString();
        }
    }
}

export function validateStartDateDatesRangeLimits({ query, permissions }: Request & PermissionsHolder, name: string) {
    if (!Object.keys(query).length) {
        return;
    }
    if (permissions?.grantedPermissions.includes(unlimitedPermission)) {
        return;
    }

    const startFrom = query[`${name}__gte`] || query[`${name}__gt`];
    if (!startFrom) {
        return;
    }
    const startDate = new Date(startFrom);
    startDate.setMonth(startDate.getMonth() + config.limits.periodLimit);
    const now = new Date();
    if (startDate < now) {
        throw new Errors.ValidationError(ERROR_RANGE_MESSAGE);
    }
}

export function validatePeriodDatesRangeLimits({ query }: Request, name: string) {
    if (!Object.keys(query).length) {
        return;
    }

    if (query[`${name}__gte`] && query[`${name}__lte`]) {
        const dateGte = new Date(query[`${name}__gte`]);
        if (isNaN(dateGte.getTime())) {
            throw new Errors.ValidationError(`${name}__gte is not valid`);
        }

        const dateLte = new Date(query[`${name}__lte`]);
        if (isNaN(dateLte.getTime())) {
            throw new Errors.ValidationError(`${name}__lte is not valid`);
        }

        const differenceInMonths =
            (dateLte.getFullYear() - dateGte.getFullYear()) * 12 +
            (dateLte.getMonth() - dateGte.getMonth()) +
            (dateLte.getDate() >= dateGte.getDate() ? 0 : -1);

        const limitMonths = config.defaultNumberOfMonthsForAggrWinBetReports;
        if (differenceInMonths > limitMonths) {
            throw new Errors.ValidationError(`${name} range shouldn't be more than ${limitMonths} months`);
        }
    }
}

export function convertDateToTimezone(date: Date, timezone: string): Date {
    if (!timezone) {
        return date;
    }
    try {
        // tslint:disable-next-line:prefer-const
        let [localMonth, localDay, localYear]: number[] =
            date.toLocaleDateString("en", { timeZone: timezone })
                .split("/")
                .map(Number);
        const [localHour, localMin, localSec]: number[] =
            date.toLocaleTimeString("en", { timeZone: timezone, hour12: false })
                .split(":")
                .map(Number);

        // month in date constructor starts from 0
        const dateNumber: number = Date.UTC(localYear, --localMonth, localDay, localHour, localMin, localSec);

        return new Date(dateNumber);
    } catch (err) {
        throw new Errors.ValidationError(`timezone ${timezone} is invalid value`);
    }
}

export function convertDateToUTC(date: Date, timezone: string): Date {
    if (!timezone) {
        return date;
    }

    try {
        const dateInTimezone: Date = convertDateToTimezone(date, timezone);
        const timeDiff: number = date.getTime() - dateInTimezone.getTime();

        return new Date(date.getTime() + timeDiff);
    } catch (err) {
        throw new Errors.ValidationError(`timezone ${timezone} is invalid value`);
    }
}

export interface UpdateDateOptions {
    value?: string | number;
    diffInDays?: number;
    diffInHours?: number;
    diffInMin?: number;
    diffInMs?: number;
}

export const updateDate = (options: UpdateDateOptions): Date => {
    let date = options.value ? new Date(options.value) : new Date();
    if (options.diffInMs) {
        date = new Date(date.setMilliseconds(date.getMilliseconds() + options.diffInMs));
    }
    return new Date(date);
};

export function setupDatesRangeLimits(datesRange: DatesRange, options: UpdateDateOptions = {}): void {
    const startDate = datesRange.ts__gte || datesRange.ts__gt;
    let endDate = datesRange.ts__lte || datesRange.ts__lt;

    // it is not mandatory to setup end date to range
    /*if (startDate && !endDate) {
        datesRange.ts__lte = updateDate(startDate, delta);
    }*/
    if (!startDate && endDate) {
        datesRange.ts__gt = updateDate({ value: endDate, diffInMs: -options.diffInMs }).toISOString();
    }
    if (!startDate && !endDate) {
        endDate = new Date().toISOString();
        datesRange.ts__gt = updateDate({ value: endDate, diffInMs: -options.diffInMs }).toISOString();
    }
}
