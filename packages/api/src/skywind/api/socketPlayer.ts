import type { Socket } from "socket.io";
import { type PlayerTokenInfo, validateToken } from "../services/playerSecurity";
import logger from "../utils/logger";
import { handleError } from "./middleware/errorMiddleware";
import { encodeEachInObject } from "../utils/publicid";
import { measureProvider } from "../utils/measures";
import { toPlayerInfo } from "../services/playerAPIService";
import { ContextVariables } from "../utils/contextVariables";

const log = logger("socket");

export default function socketPlayer(socket: Socket & { tokenData?: PlayerTokenInfo }): void {
    socket.on("get-player-info", async (params?: { token?: string }): Promise<void> => {
        return measureProvider.runInTransaction("get-player-info", async () => {
            try {
                let tokenData: PlayerTokenInfo;
                if (socket.tokenData) {
                    tokenData = socket.tokenData;
                } else {
                    tokenData = await validateToken(params?.token)
                }
                ContextVariables.setPlayerAuthData(tokenData);
                const playerInfo = await toPlayerInfo({
                    ...tokenData,
                    tokenData
                }, log);
                socket.emit("player-info", encodeEachInObject(playerInfo));
            } catch (error) {
                emitError(socket, error);
            }
        });
    });
}

export function emitError(socket: Socket & { tokenData?: PlayerTokenInfo }, err) {
    measureProvider.saveError(err);
    const error = handleError(err, log);
    socket.emit("player-error", {
        code: error?.code,
        message: error?.message,
        extraData: {
            ...error?.extraData,
            traceId: measureProvider.getTraceID()
        },
    });
}
